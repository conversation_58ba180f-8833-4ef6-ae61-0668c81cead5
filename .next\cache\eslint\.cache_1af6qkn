[{"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\create-payment-intent\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\history\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AnalysisHistory.tsx": "8", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AuthModal.tsx": "9", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\BuyCreditsModal.tsx": "10", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\PhotoUpload.tsx": "11", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\UserProfile.tsx": "12", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx": "13", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\credits.ts": "14", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\openai.ts": "15", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\stripe.ts": "16", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase-server.ts": "17", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase.ts": "18", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\utils.ts": "19"}, {"size": 3642, "mtime": 1752229580854, "results": "20", "hashOfConfig": "21"}, {"size": 1652, "mtime": 1752229618610, "results": "22", "hashOfConfig": "21"}, {"size": 1827, "mtime": 1752229594167, "results": "23", "hashOfConfig": "21"}, {"size": 1325, "mtime": 1752229606943, "results": "24", "hashOfConfig": "21"}, {"size": 2376, "mtime": 1752229382840, "results": "25", "hashOfConfig": "21"}, {"size": 762, "mtime": 1752226804531, "results": "26", "hashOfConfig": "21"}, {"size": 5000, "mtime": 1752226891793, "results": "27", "hashOfConfig": "21"}, {"size": 7174, "mtime": 1752229456824, "results": "28", "hashOfConfig": "21"}, {"size": 5173, "mtime": 1752229101935, "results": "29", "hashOfConfig": "21"}, {"size": 5860, "mtime": 1752229142699, "results": "30", "hashOfConfig": "21"}, {"size": 9250, "mtime": 1752229199608, "results": "31", "hashOfConfig": "21"}, {"size": 4605, "mtime": 1752226557438, "results": "32", "hashOfConfig": "21"}, {"size": 3053, "mtime": 1752229337137, "results": "33", "hashOfConfig": "21"}, {"size": 4792, "mtime": 1752229568155, "results": "34", "hashOfConfig": "21"}, {"size": 4857, "mtime": 1752229256108, "results": "35", "hashOfConfig": "21"}, {"size": 2194, "mtime": 1752229491305, "results": "36", "hashOfConfig": "21"}, {"size": 800, "mtime": 1752229526889, "results": "37", "hashOfConfig": "21"}, {"size": 2144, "mtime": 1752229243287, "results": "38", "hashOfConfig": "21"}, {"size": 1105, "mtime": 1752226485382, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2aqmsv", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\create-payment-intent\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\history\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AnalysisHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AuthModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\BuyCreditsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\PhotoUpload.tsx", [], ["97"], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\credits.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\openai.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\stripe.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase-server.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\utils.ts", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 125, "column": 13, "nodeType": "100", "endLine": 129, "endColumn": 15, "suppressions": "101"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["102"], {"kind": "103", "justification": "104"}, "directive", ""]
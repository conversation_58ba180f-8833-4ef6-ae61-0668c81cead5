[{"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\create-payment-intent\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\history\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AnalysisHistory.tsx": "8", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AuthModal.tsx": "9", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\BuyCreditsModal.tsx": "10", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\PhotoUpload.tsx": "11", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\UserProfile.tsx": "12", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx": "13", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\credits.ts": "14", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\openai.ts": "15", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\stripe.ts": "16", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase-server.ts": "17", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase.ts": "18", "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\utils.ts": "19"}, {"size": 3636, "mtime": 1752227093008, "results": "20", "hashOfConfig": "21"}, {"size": 1646, "mtime": 1752227303633, "results": "22", "hashOfConfig": "21"}, {"size": 1821, "mtime": 1752227282969, "results": "23", "hashOfConfig": "21"}, {"size": 1319, "mtime": 1752227293207, "results": "24", "hashOfConfig": "21"}, {"size": 2409, "mtime": 1752228770090, "results": "25", "hashOfConfig": "21"}, {"size": 762, "mtime": 1752226804531, "results": "26", "hashOfConfig": "21"}, {"size": 5000, "mtime": 1752226891793, "results": "27", "hashOfConfig": "21"}, {"size": 7073, "mtime": 1752226775226, "results": "28", "hashOfConfig": "21"}, {"size": 5185, "mtime": 1752226533369, "results": "29", "hashOfConfig": "21"}, {"size": 5950, "mtime": 1752226917840, "results": "30", "hashOfConfig": "21"}, {"size": 9193, "mtime": 1752226736586, "results": "31", "hashOfConfig": "21"}, {"size": 4605, "mtime": 1752226557438, "results": "32", "hashOfConfig": "21"}, {"size": 2970, "mtime": 1752226511005, "results": "33", "hashOfConfig": "21"}, {"size": 4774, "mtime": 1752228728914, "results": "34", "hashOfConfig": "21"}, {"size": 4870, "mtime": 1752226464077, "results": "35", "hashOfConfig": "21"}, {"size": 2195, "mtime": 1752226477055, "results": "36", "hashOfConfig": "21"}, {"size": 750, "mtime": 1752227074846, "results": "37", "hashOfConfig": "21"}, {"size": 2124, "mtime": 1752228689761, "results": "38", "hashOfConfig": "21"}, {"size": 1105, "mtime": 1752226485382, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2aqmsv", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\create-payment-intent\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\history\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AnalysisHistory.tsx", ["97"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\AuthModal.tsx", ["98", "99"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\BuyCreditsModal.tsx", ["100", "101", "102", "103"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\PhotoUpload.tsx", ["104", "105", "106"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\components\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx", ["107", "108", "109"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\credits.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\openai.ts", ["110"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\stripe.ts", [], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase-server.ts", ["111", "112"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\supabase.ts", ["113"], [], "C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\lib\\utils.ts", [], [], {"ruleId": "114", "severity": 1, "message": "115", "line": 53, "column": 6, "nodeType": "116", "endLine": 53, "endColumn": 12, "suggestions": "117"}, {"ruleId": "118", "severity": 2, "message": "119", "line": 5, "column": 25, "nodeType": null, "messageId": "120", "endLine": 5, "endColumn": 29}, {"ruleId": "118", "severity": 2, "message": "121", "line": 42, "column": 14, "nodeType": null, "messageId": "120", "endLine": 42, "endColumn": 17}, {"ruleId": "118", "severity": 2, "message": "122", "line": 8, "column": 10, "nodeType": null, "messageId": "120", "endLine": 8, "endColumn": 25}, {"ruleId": "118", "severity": 2, "message": "123", "line": 9, "column": 10, "nodeType": null, "messageId": "120", "endLine": 9, "endColumn": 21}, {"ruleId": "118", "severity": 2, "message": "121", "line": 61, "column": 14, "nodeType": null, "messageId": "120", "endLine": 61, "endColumn": 17}, {"ruleId": "124", "severity": 2, "message": "125", "line": 179, "column": 60, "nodeType": "126", "messageId": "127", "suggestions": "128"}, {"ruleId": "118", "severity": 2, "message": "129", "line": 33, "column": 11, "nodeType": null, "messageId": "120", "endLine": 33, "endColumn": 15}, {"ruleId": "118", "severity": 2, "message": "130", "line": 33, "column": 17, "nodeType": null, "messageId": "120", "endLine": 33, "endColumn": 24}, {"ruleId": "131", "severity": 1, "message": "132", "line": 124, "column": 13, "nodeType": "133", "endLine": 128, "endColumn": 15}, {"ruleId": "134", "severity": 2, "message": "135", "line": 12, "column": 65, "nodeType": "136", "messageId": "137", "endLine": 12, "endColumn": 68, "suggestions": "138"}, {"ruleId": "134", "severity": 2, "message": "135", "line": 13, "column": 65, "nodeType": "136", "messageId": "137", "endLine": 13, "endColumn": 68, "suggestions": "139"}, {"ruleId": "114", "severity": 1, "message": "140", "line": 80, "column": 6, "nodeType": "116", "endLine": 80, "endColumn": 8, "suggestions": "141"}, {"ruleId": "118", "severity": 2, "message": "142", "line": 78, "column": 14, "nodeType": null, "messageId": "120", "endLine": 78, "endColumn": 24}, {"ruleId": "134", "severity": 2, "message": "135", "line": 16, "column": 49, "nodeType": "136", "messageId": "137", "endLine": 16, "endColumn": 52, "suggestions": "143"}, {"ruleId": "134", "severity": 2, "message": "135", "line": 19, "column": 37, "nodeType": "136", "messageId": "137", "endLine": 19, "endColumn": 40, "suggestions": "144"}, {"ruleId": "134", "severity": 2, "message": "135", "line": 71, "column": 20, "nodeType": "136", "messageId": "137", "endLine": 71, "endColumn": 23, "suggestions": "145"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchHistory'. Either include it or remove the dependency array.", "ArrayExpression", ["146"], "@typescript-eslint/no-unused-vars", "'User' is defined but never used.", "unusedVar", "'err' is defined but never used.", "'CREDIT_PACKAGES' is defined but never used.", "'formatPrice' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["147", "148", "149", "150"], "'user' is assigned a value but never used.", "'profile' is assigned a value but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["151", "152"], ["153", "154"], "React Hook useEffect has missing dependencies: 'fetchProfile' and 'supabase.auth'. Either include them or remove the dependency array.", ["155"], "'parseError' is defined but never used.", ["156", "157"], ["158", "159"], ["160", "161"], {"desc": "162", "fix": "163"}, {"messageId": "164", "data": "165", "fix": "166", "desc": "167"}, {"messageId": "164", "data": "168", "fix": "169", "desc": "170"}, {"messageId": "164", "data": "171", "fix": "172", "desc": "173"}, {"messageId": "164", "data": "174", "fix": "175", "desc": "176"}, {"messageId": "177", "fix": "178", "desc": "179"}, {"messageId": "180", "fix": "181", "desc": "182"}, {"messageId": "177", "fix": "183", "desc": "179"}, {"messageId": "180", "fix": "184", "desc": "182"}, {"desc": "185", "fix": "186"}, {"messageId": "177", "fix": "187", "desc": "179"}, {"messageId": "180", "fix": "188", "desc": "182"}, {"messageId": "177", "fix": "189", "desc": "179"}, {"messageId": "180", "fix": "190", "desc": "182"}, {"messageId": "177", "fix": "191", "desc": "179"}, {"messageId": "180", "fix": "192", "desc": "182"}, "Update the dependencies array to be: [fetchHistory, user]", {"range": "193", "text": "194"}, "replaceWithAlt", {"alt": "195"}, {"range": "196", "text": "197"}, "Replace with `&apos;`.", {"alt": "198"}, {"range": "199", "text": "200"}, "Replace with `&lsquo;`.", {"alt": "201"}, {"range": "202", "text": "203"}, "Replace with `&#39;`.", {"alt": "204"}, {"range": "205", "text": "206"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "207", "text": "208"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "209", "text": "210"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "211", "text": "208"}, {"range": "212", "text": "210"}, "Update the dependencies array to be: [fetchProfile, supabase.auth]", {"range": "213", "text": "214"}, {"range": "215", "text": "208"}, {"range": "216", "text": "210"}, {"range": "217", "text": "208"}, {"range": "218", "text": "210"}, {"range": "219", "text": "208"}, {"range": "220", "text": "210"}, [1434, 1440], "[fetchHist<PERSON>, user]", "&apos;", [5732, 5836], "\n                  Your payment is secured by Stripe. We don&apos;t store your card details.\n                ", "&lsquo;", [5732, 5836], "\n                  Your payment is secured by Stripe. We don&lsquo;t store your card details.\n                ", "&#39;", [5732, 5836], "\n                  Your payment is secured by Stripe. We don&#39;t store your card details.\n                ", "&rsquo;", [5732, 5836], "\n                  Your payment is secured by Stripe. We don&rsquo;t store your card details.\n                ", [403, 406], "unknown", [403, 406], "never", [474, 477], [474, 477], [2121, 2123], "[fetchProfile, supabase.auth]", [558, 561], [558, 561], [663, 666], [663, 666], [1905, 1908], [1905, 1908]]
(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>a});var s=r(7413),o=r(5041),n=r.n(o);r(1135);var i=r(9131);let a={title:"Guess My Age - AI Age Estimation",description:"Upload a photo and let AI estimate your age with advanced computer vision technology.",keywords:"age estimation, AI, photo analysis, computer vision"};function u({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().className} antialiased bg-gray-50`,children:(0,s.jsx)(i.AuthProvider,{children:e})})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5795:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6362:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},6558:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>p,A:()=>h});var s=r(687),o=r(3210),n=r(463),i=r(9605);let a=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",u=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",d=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",l=()=>{let e=a(),t=u();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,i.createBrowserClient)(e,t)};(()=>{let e=a(),t=u();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,n.UU)(e,t)})(),(()=>{let e=a(),t=d();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,n.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})})();let c=(0,o.createContext)(void 0);function p({children:e}){let[t,r]=(0,o.useState)(null),[n,i]=(0,o.useState)(null),[a,u]=(0,o.useState)(!0),d=l(),p=async e=>{try{let{data:t,error:r}=await d.from("user_profiles").select("*").eq("id",e).single();if(r)return void console.error("Error fetching profile:",r);i(t)}catch(e){console.error("Error fetching profile:",e)}},h=async()=>{t&&await p(t.id)},m=async(e,t)=>{let{error:r}=await d.auth.signInWithPassword({email:e,password:t});return{error:r}},v=async(e,t)=>{let{error:r}=await d.auth.signUp({email:e,password:t});return{error:r}},x=async()=>{await d.auth.signOut()};return(0,s.jsx)(c.Provider,{value:{user:t,profile:n,loading:a,signIn:m,signUp:v,signOut:x,refreshProfile:h},children:e})}function h(){let e=(0,o.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8835:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(5239),o=r(8088),n=r(8170),i=r.n(n),a=r(893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(2907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9410:(e,t,r)=>{Promise.resolve().then(r.bind(r,6558))},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{},9771:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,925],()=>r(8835));module.exports=s})();
(()=>{var e={};e.id=786,e.ids=[786],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,s)=>{"use strict";function r(e){let t=e.headers.get("x-forwarded-for"),s=e.headers.get("x-real-ip"),r=e.headers.get("cf-connecting-ip");return r||s||(t?t.split(",")[0].trim():"127.0.0.1")}s.d(t,{Tf:()=>r})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>a,hS:()=>i,sy:()=>o});var r=s(3769),n=s(6621);async function i(e,t){let s=(0,r.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let r=new Date().toISOString().split("T")[0],n=t.last_use_date,i=t.daily_uses;return(n!==r&&(i=0,await s.from("user_profiles").update({daily_uses:0,last_use_date:r}).eq("id",e)),i<3)?{canUse:!0,remainingUses:3-i,isAnonymous:!1,needsCredits:!1}:t.credits>0?{canUse:!0,remainingUses:t.credits,isAnonymous:!1,needsCredits:!1}:{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!0}}{if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],s=(0,n.vZ)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e),i=1>(r?.length||0);return{canUse:i,remainingUses:+!!i,isAnonymous:!0,needsCredits:!1}}}async function a(e,t){let s=(0,r.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return!1;let r=new Date().toISOString().split("T")[0],n=t.daily_uses;return(t.last_use_date!==r&&(n=0),n<3)?(await s.from("user_profiles").update({daily_uses:n+1,last_use_date:r,updated_at:new Date().toISOString()}).eq("id",e),!0):t.credits>0&&(await s.from("user_profiles").update({credits:t.credits-1,updated_at:new Date().toISOString()}).eq("id",e),!0)}{if(!t)return!1;let e=new Date().toISOString().split("T")[0],s=(0,n.vZ)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e).single();return!r&&(await s.from("anonymous_uses").insert({ip_address:t,use_date:e}),!0)}}async function o(e,t){let s=(0,r.d)(),{data:n}=await s.from("user_profiles").select("credits").eq("id",e).single();return!!n&&(await s.from("user_profiles").update({credits:n.credits+t,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var r=s(4386),n=s(4999);let i=()=>{let e=(0,n.UL)();return(0,r.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){e.set({name:t,value:s,...r})},remove(t,s){e.set({name:t,value:"",...s})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6621:(e,t,s)=>{"use strict";s.d(t,{vZ:()=>o});var r=s(6437);s(4386);let n=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",i=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",a=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",o=()=>{let e=n(),t=a();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,r.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})};(()=>{let e=n(),t=i();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,r.UU)(e,t)})(),o()},7437:(e,t,s)=>{"use strict";let r,n,i,a;s.r(t),s.d(t,{patchFetch:()=>rw,routeModule:()=>rp,serverHooks:()=>ry,workAsyncStorage:()=>rm,workUnitAsyncStorage:()=>rg});var o,l,c,u,h,d,f,p,m,g,y,w,_,b,v,x,S,A,I,$,O,R,k,E,P,C,N,T,j,M,D,L,U,B,q,W,F,X,J,H,z,V,K,Y,G,Q,Z,ee,et,es,er,en,ei,ea,eo,el,ec,eu,eh,ed,ef,ep,em,eg,ey,ew,e_,eb,ev,ex,eS,eA,eI,e$,eO,eR,ek={};s.r(ek),s.d(ek,{POST:()=>rf});var eE=s(6559),eP=s(8088),eC=s(7719),eN=s(2190),eT=s(3769);function ej(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function eM(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}let eD=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eD=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eL(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eU=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eB extends Error{}class eq extends eB{constructor(e,t,s,r){super(`${eq.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){if(!e||!r)return new eF({message:s,cause:eU(t)});let n=t?.error;return 400===e?new eJ(e,n,s,r):401===e?new eH(e,n,s,r):403===e?new ez(e,n,s,r):404===e?new eV(e,n,s,r):409===e?new eK(e,n,s,r):422===e?new eY(e,n,s,r):429===e?new eG(e,n,s,r):e>=500?new eQ(e,n,s,r):new eq(e,n,s,r)}}class eW extends eq{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eF extends eq{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eX extends eF{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eJ extends eq{}class eH extends eq{}class ez extends eq{}class eV extends eq{}class eK extends eq{}class eY extends eq{}class eG extends eq{}class eQ extends eq{}class eZ extends eB{constructor(){super("Could not parse response content as the length limit was reached")}}class e0 extends eB{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class e1 extends Error{constructor(e){super(e)}}let e2=/^[a-z][a-z0-9+.-]*:/i,e3=e=>e2.test(e),e4=e=>(e4=Array.isArray)(e),e6=e4;function e5(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let e8=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eB(`${e} must be an integer`);if(t<0)throw new eB(`${e} must be a positive integer`);return t},e9=e=>{try{return JSON.parse(e)}catch(e){return}},e7=e=>new Promise(t=>setTimeout(t,e)),te="5.9.0",tt=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,ts=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tn(Deno.build.os),"X-Stainless-Arch":tr(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tn(globalThis.process.platform??"unknown"),"X-Stainless-Arch":tr(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tr=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tn=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",ti=()=>r??(r=ts());function ta(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function to(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return ta({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function tl(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tc(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tu=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),th="RFC3986",td=e=>String(e),tf={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:td},tp=(e,t)=>(tp=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),tm=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tg(e,t){if(e4(e)){let s=[];for(let r=0;r<e.length;r+=1)s.push(t(e[r]));return s}return t(e)}let ty={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tw=function(e,t){Array.prototype.push.apply(e,e4(t)?t:[t])},t_={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,r,n)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let r=t.charCodeAt(e);if(45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===n&&(40===r||41===r)){s[s.length]=t.charAt(e);continue}if(r<128){s[s.length]=tm[r];continue}if(r<2048){s[s.length]=tm[192|r>>6]+tm[128|63&r];continue}if(r<55296||r>=57344){s[s.length]=tm[224|r>>12]+tm[128|r>>6&63]+tm[128|63&r];continue}e+=1,r=65536+((1023&r)<<10|1023&t.charCodeAt(e)),s[s.length]=tm[240|r>>18]+tm[128|r>>12&63]+tm[128|r>>6&63]+tm[128|63&r]}a+=s.join("")}return a},encodeValuesOnly:!1,format:th,formatter:td,indices:!1,serializeDate:e=>(n??(n=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},tb={};function tv(e){let t;return(i??(i=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tx(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class tS{constructor(){o.set(this,void 0),l.set(this,void 0),ej(this,o,new Uint8Array,"f"),ej(this,l,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tv(e):e;ej(this,o,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([eM(this,o,"f"),s]),"f");let r=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eM(this,o,"f"),eM(this,l,"f")));){if(t.carriage&&null==eM(this,l,"f")){ej(this,l,t.index,"f");continue}if(null!=eM(this,l,"f")&&(t.index!==eM(this,l,"f")+1||t.carriage)){r.push(tx(eM(this,o,"f").subarray(0,eM(this,l,"f")-1))),ej(this,o,eM(this,o,"f").subarray(eM(this,l,"f")),"f"),ej(this,l,null,"f");continue}let e=null!==eM(this,l,"f")?t.preceding-1:t.preceding,s=tx(eM(this,o,"f").subarray(0,e));r.push(s),ej(this,o,eM(this,o,"f").subarray(t.index),"f"),ej(this,l,null,"f")}return r}flush(){return eM(this,o,"f").length?this.decode("\n"):[]}}o=new WeakMap,l=new WeakMap,tS.NEWLINE_CHARS=new Set(["\n","\r"]),tS.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let tA={off:0,error:200,warn:300,info:400,debug:500},tI=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(tA,e))return e;tE(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tA))}`)}};function t$(){}function tO(e,t,s){return!t||tA[e]>tA[s]?t$:t[e].bind(t)}let tR={error:t$,warn:t$,info:t$,debug:t$},tk=new WeakMap;function tE(e){let t=e.logger,s=e.logLevel??"off";if(!t)return tR;let r=tk.get(t);if(r&&r[0]===s)return r[1];let n={error:tO("error",t,s),warn:tO("warn",t,s),info:tO("info",t,s),debug:tO("debug",t,s)};return tk.set(t,[s,n]),n}let tP=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);class tC{constructor(e,t,s){this.iterator=e,c.set(this,void 0),this.controller=t,ej(this,c,s,"f")}static fromSSEResponse(e,t,s){let r=!1,n=s?tE(s):console;async function*i(){if(r)throw new eB("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let s=!1;try{for await(let r of tN(e,t))if(!s){if(r.data.startsWith("[DONE]")){s=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw n.error("Could not parse message into JSON:",r.data),n.error("From chunk:",r.raw),e}if(t&&t.error)throw new eq(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new eq(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}s=!0}catch(e){if(eL(e))return;throw e}finally{s||t.abort()}}return new tC(i,t,s)}static fromReadableStream(e,t,s){let r=!1;async function*n(){let t=new tS;for await(let s of tl(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tC(async function*(){if(r)throw new eB("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eL(e))return;throw e}finally{e||t.abort()}},t,s)}[(c=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new tC(()=>r(e),this.controller,eM(this,c,"f")),new tC(()=>r(t),this.controller,eM(this,c,"f"))]}toReadableStream(){let e,t=this;return ta({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=tv(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tN(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eB("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eB("Attempted to iterate over a response with no body")}let s=new tj,r=new tS;for await(let t of tT(tl(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*tT(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tv(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tj{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function tM(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(tE(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller,e):tC.fromSSEResponse(s,t.controller,e);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?tD(await s.json(),s):await s.text()})();return tE(e).debug(`[${r}] response parsed`,tP({retryOfRequestLogID:n,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function tD(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tL extends Promise{constructor(e,t,s=tM){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,u.set(this,void 0),ej(this,u,e,"f")}_thenUnwrap(e){return new tL(eM(this,u,"f"),this.responsePromise,async(t,s)=>tD(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eM(this,u,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}u=new WeakMap;class tU{constructor(e,t,s,r){h.set(this,void 0),ej(this,h,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eB("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eM(this,h,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(h=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tB extends tL{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tM(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tq extends tU{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tW extends tU{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tF=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tX(e,t,s){return tF(),new File(e,t??"unknown_file",s)}function tJ(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tH=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tz=async(e,t)=>({...e,body:await tK(e.body,t)}),tV=new WeakMap,tK=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tV.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tV.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tZ(s,e,t))),s},tY=e=>e instanceof Blob&&"name"in e,tG=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tH(e)||tY(e)),tQ=e=>{if(tG(e))return!0;if(Array.isArray(e))return e.some(tQ);if(e&&"object"==typeof e){for(let t in e)if(tQ(e[t]))return!0}return!1},tZ=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tX([await s.blob()],tJ(s)));else if(tH(s))e.append(t,tX([await new Response(to(s)).blob()],tJ(s)));else if(tY(s))e.append(t,s,tJ(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tZ(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>tZ(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},t0=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,t1=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&t0(e),t2=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function t3(e,t,s){if(tF(),t1(e=await e))return e instanceof File?e:tX([await e.arrayBuffer()],e.name);if(t2(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tX(await t4(r),t,s)}let r=await t4(e);if(t||(t=tJ(e)),!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tX(r,t,s)}async function t4(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(t0(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tH(e))for await(let s of e)t.push(...await t4(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class t6{constructor(e){this._client=e}}function t5(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t8=Object.freeze(Object.create(null)),t9=((e=t5)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,i=[],a=t.reduce((t,r,a)=>{/[?#]/.test(r)&&(n=!0);let o=s[a],l=(n?encodeURIComponent:e)(""+o);return a!==s.length&&(null==o||"object"==typeof o&&o.toString===Object.getPrototypeOf(Object.getPrototypeOf(o.hasOwnProperty??t8)??t8)?.toString)&&(l=o+"",i.push({start:t.length+r.length,length:l.length,error:`Value of type ${Object.prototype.toString.call(o).slice(8,-1)} is not a valid path parameter`})),t+r+(a===s.length?"":l)},""),o=a.split(/[?#]/,1)[0],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(o));)i.push({start:r.index,length:r[0].length,error:`Value "${r[0]}" can't be safely passed as a path parameter`});if(i.sort((e,t)=>e.start-t.start),i.length>0){let e=0,t=i.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new eB(`Path parameters result in path with invalid segments:
${i.map(e=>e.error).join("\n")}
${a}
${t}`)}return a})(t5);class t7 extends t6{list(e,t={},s){return this._client.getAPIList(t9`/chat/completions/${e}/messages`,tW,{query:t,...s})}}let se=e=>e?.role==="assistant",st=e=>e?.role==="tool";class ss{constructor(){d.add(this),this.controller=new AbortController,f.set(this,void 0),p.set(this,()=>{}),m.set(this,()=>{}),g.set(this,void 0),y.set(this,()=>{}),w.set(this,()=>{}),_.set(this,{}),b.set(this,!1),v.set(this,!1),x.set(this,!1),S.set(this,!1),ej(this,f,new Promise((e,t)=>{ej(this,p,e,"f"),ej(this,m,t,"f")}),"f"),ej(this,g,new Promise((e,t)=>{ej(this,y,e,"f"),ej(this,w,t,"f")}),"f"),eM(this,f,"f").catch(()=>{}),eM(this,g,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eM(this,d,"m",A).bind(this))},0)}_connected(){this.ended||(eM(this,p,"f").call(this),this._emit("connect"))}get ended(){return eM(this,b,"f")}get errored(){return eM(this,v,"f")}get aborted(){return eM(this,x,"f")}abort(){this.controller.abort()}on(e,t){return(eM(this,_,"f")[e]||(eM(this,_,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eM(this,_,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(eM(this,_,"f")[e]||(eM(this,_,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ej(this,S,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ej(this,S,!0,"f"),await eM(this,g,"f")}_emit(e,...t){if(eM(this,b,"f"))return;"end"===e&&(ej(this,b,!0,"f"),eM(this,y,"f").call(this));let s=eM(this,_,"f")[e];if(s&&(eM(this,_,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eM(this,S,"f")||s?.length||Promise.reject(e),eM(this,m,"f").call(this,e),eM(this,w,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eM(this,S,"f")||s?.length||Promise.reject(e),eM(this,m,"f").call(this,e),eM(this,w,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function sr(e){return e?.$brand==="auto-parseable-response-format"}function sn(e){return e?.$brand==="auto-parseable-tool"}function si(e,t){let s=e.choices.map(e=>{var s,r;if("length"===e.finish_reason)throw new eZ;if("content_filter"===e.finish_reason)throw new e0;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:sn(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,r=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(r):JSON.parse(r):null):null}}});return{...e,choices:s}}function sa(e){return!!sr(e.response_format)||(e.tools?.some(e=>sn(e)||"function"===e.type&&!0===e.function.strict)??!1)}f=new WeakMap,p=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,x=new WeakMap,S=new WeakMap,d=new WeakSet,A=function(e){if(ej(this,v,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eW),e instanceof eW)return ej(this,x,!0,"f"),this._emit("abort",e);if(e instanceof eB)return this._emit("error",e);if(e instanceof Error){let t=new eB(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eB(String(e)))};class so extends ss{constructor(){super(...arguments),I.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),st(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(se(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eB("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eM(this,I,"m",$).call(this)}async finalMessage(){return await this.done(),eM(this,I,"m",O).call(this)}async finalFunctionToolCall(){return await this.done(),eM(this,I,"m",R).call(this)}async finalFunctionToolCallResult(){return await this.done(),eM(this,I,"m",k).call(this)}async totalUsage(){return await this.done(),eM(this,I,"m",E).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eM(this,I,"m",O).call(this);t&&this._emit("finalMessage",t);let s=eM(this,I,"m",$).call(this);s&&this._emit("finalContent",s);let r=eM(this,I,"m",R).call(this);r&&this._emit("finalFunctionToolCall",r);let n=eM(this,I,"m",k).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eM(this,I,"m",E).call(this))}async _createChatCompletion(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eM(this,I,"m",P).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(si(n,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let r="tool",{tool_choice:n="auto",stream:i,...a}=t,o="string"!=typeof n&&n?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(sn(e)){if(!e.$callback)throw new eB("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:n,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new eB("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:n,arguments:i}=e.function,a=u[n];if(a){if(o&&o!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:r,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=eM(this,I,"m",C).call(this,l);if(this._addMessage({role:r,tool_call_id:s,content:c}),o)return}}}}I=new WeakSet,$=function(){return eM(this,I,"m",O).call(this).content??null},O=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(se(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eB("stream ended without producing a ChatCompletionMessage with role=assistant")},R=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(se(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},k=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(st(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},E=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},P=function(e){if(null!=e.n&&e.n>1)throw new eB("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},C=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class sl extends so{static runTools(e,t,s){let r=new sl,n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}_addMessage(e,t=!0){super._addMessage(e,t),se(e)&&e.content&&this._emit("content",e.content)}}let sc={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class su extends Error{}class sh extends Error{}let sd=(e,t)=>{let s=e.length,r=0,n=e=>{throw new su(`${e} at position ${r}`)},i=e=>{throw new sh(`${e} at position ${r}`)},a=()=>(h(),r>=s&&n("Unexpected end of input"),'"'===e[r])?o():"{"===e[r]?l():"["===e[r]?c():"null"===e.substring(r,r+4)||sc.NULL&t&&s-r<4&&"null".startsWith(e.substring(r))?(r+=4,null):"true"===e.substring(r,r+4)||sc.BOOL&t&&s-r<4&&"true".startsWith(e.substring(r))?(r+=4,!0):"false"===e.substring(r,r+5)||sc.BOOL&t&&s-r<5&&"false".startsWith(e.substring(r))?(r+=5,!1):"Infinity"===e.substring(r,r+8)||sc.INFINITY&t&&s-r<8&&"Infinity".startsWith(e.substring(r))?(r+=8,1/0):"-Infinity"===e.substring(r,r+9)||sc.MINUS_INFINITY&t&&1<s-r&&s-r<9&&"-Infinity".startsWith(e.substring(r))?(r+=9,-1/0):"NaN"===e.substring(r,r+3)||sc.NAN&t&&s-r<3&&"NaN".startsWith(e.substring(r))?(r+=3,NaN):u(),o=()=>{let a=r,o=!1;for(r++;r<s&&('"'!==e[r]||o&&"\\"===e[r-1]);)o="\\"===e[r]&&!o,r++;if('"'==e.charAt(r))try{return JSON.parse(e.substring(a,++r-Number(o)))}catch(e){i(String(e))}else if(sc.STR&t)try{return JSON.parse(e.substring(a,r-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{r++,h();let i={};try{for(;"}"!==e[r];){if(h(),r>=s&&sc.OBJ&t)return i;let n=o();h(),r++;try{let e=a();Object.defineProperty(i,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(sc.OBJ&t)return i;throw e}h(),","===e[r]&&r++}}catch(e){if(sc.OBJ&t)return i;n("Expected '}' at end of object")}return r++,i},c=()=>{r++;let s=[];try{for(;"]"!==e[r];)s.push(a()),h(),","===e[r]&&r++}catch(e){if(sc.ARR&t)return s;n("Expected ']' at end of array")}return r++,s},u=()=>{if(0===r){"-"===e&&sc.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(sc.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=r;for("-"===e[r]&&r++;e[r]&&!",]}".includes(e[r]);)r++;r!=s||sc.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(a,r))}catch(s){"-"===e.substring(a,r)&&sc.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;r<s&&" \n\r	".includes(e[r]);)r++};return a()},sf=e=>(function(e,t=sc.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return sd(e.trim(),t)})(e,sc.ALL^sc.NUM);class sp extends so{constructor(e){super(),N.add(this),T.set(this,void 0),j.set(this,void 0),M.set(this,void 0),ej(this,T,e,"f"),ej(this,j,[],"f")}get currentChatCompletionSnapshot(){return eM(this,M,"f")}static fromReadableStream(e){let t=new sp(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let r=new sp(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,s){super._createChatCompletion;let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eM(this,N,"m",D).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),n))eM(this,N,"m",U).call(this,e);if(n.controller.signal?.aborted)throw new eW;return this._addChatCompletion(eM(this,N,"m",W).call(this))}async _fromReadableStream(e,t){let s,r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eM(this,N,"m",D).call(this),this._connected();let n=tC.fromReadableStream(e,this.controller);for await(let e of n)s&&s!==e.id&&this._addChatCompletion(eM(this,N,"m",W).call(this)),eM(this,N,"m",U).call(this,e),s=e.id;if(n.controller.signal?.aborted)throw new eW;return this._addChatCompletion(eM(this,N,"m",W).call(this))}[(T=new WeakMap,j=new WeakMap,M=new WeakMap,N=new WeakSet,D=function(){this.ended||ej(this,M,void 0,"f")},L=function(e){let t=eM(this,j,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eM(this,j,"f")[e.index]=t),t},U=function(e){if(this.ended)return;let t=eM(this,N,"m",X).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let r=eM(this,N,"m",L).call(this,e);for(let t of(e.finish_reason&&(eM(this,N,"m",q).call(this,e),null!=r.current_tool_call_index&&eM(this,N,"m",B).call(this,e,r.current_tool_call_index)),s.delta.tool_calls??[]))r.current_tool_call_index!==t.index&&(eM(this,N,"m",q).call(this,e),null!=r.current_tool_call_index&&eM(this,N,"m",B).call(this,e,r.current_tool_call_index)),r.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},B=function(e,t){if(eM(this,N,"m",L).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eM(this,T,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:sn(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},q=function(e){let t=eM(this,N,"m",L).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eM(this,N,"m",F).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},W=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");let e=eM(this,M,"f");if(!e)throw new eB("request ended without sending any chunks");return ej(this,M,void 0,"f"),ej(this,j,[],"f"),function(e,t){var s;let{id:r,choices:n,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:r,choices:n.map(({message:t,finish_reason:s,index:r,logprobs:n,...i})=>{if(!s)throw new eB(`missing finish_reason for choice ${r}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eB(`missing role for choice ${r}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eB(`missing function_call.arguments for choice ${r}`);if(!l)throw new eB(`missing function_call.name for choice ${r}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}return l?{...i,index:r,finish_reason:s,logprobs:n,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:n,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=n||{};if(null==a)throw new eB(`missing choices[${r}].tool_calls[${s}].id
${sm(e)}`);if(null==i)throw new eB(`missing choices[${r}].tool_calls[${s}].type
${sm(e)}`);if(null==c)throw new eB(`missing choices[${r}].tool_calls[${s}].function.name
${sm(e)}`);if(null==l)throw new eB(`missing choices[${r}].tool_calls[${s}].function.arguments
${sm(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&sa(t)?si(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eM(this,T,"f"))},F=function(){let e=eM(this,T,"f")?.response_format;return sr(e)?e:null},X=function(e){var t,s,r,n;let i=eM(this,M,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=ej(this,M,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:r,refusal:n,...i}=u;Object.assign(e.logprobs,i),r&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...r)),n&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eM(this,T,"f")&&sa(eM(this,T,"f")))){if("length"===l)throw new eZ;if("content_filter"===l)throw new e0}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:f,role:p,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((r=e.message.function_call).arguments??(r.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eM(this,N,"m",F).call(this)&&(e.message.parsed=sf(e.message.content))),m)for(let{index:t,id:s,type:r,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(o,a),s&&(o.id=s),r&&(o.type=r),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return sn(s)||s?.function.strict||!1}(eM(this,T,"f"),o)&&(o.function.parsed_arguments=sf(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tC(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sm(e){return JSON.stringify(e)}class sg extends sp{static fromReadableStream(e){let t=new sg(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let r=new sg(t),n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}}class sy extends t6{constructor(){super(...arguments),this.messages=new t7(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(t9`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(t9`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eB(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eB(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>si(t,e))}runTools(e,t){return e.stream?sg.runTools(this._client,e,t):sl.runTools(this._client,e,t)}stream(e,t){return sp.createChatCompletion(this._client,e,t)}}sy.Messages=t7;class sw extends t6{constructor(){super(...arguments),this.completions=new sy(this._client)}}sw.Completions=sy;let s_=Symbol("brand.privateNullableHeaders"),sb=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,i]of function*(e){let t;if(!e)return;if(s_ in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():e6(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=e6(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===i?(t.delete(n),s.add(r)):(t.append(n,i),s.delete(r))}}return{[s_]:!0,values:t,nulls:s}};class sv extends t6{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sb([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sx extends t6{create(e,t){return this._client.post("/audio/transcriptions",tz({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sS extends t6{create(e,t){return this._client.post("/audio/translations",tz({body:e,...t,__metadata:{model:e.model}},this._client))}}class sA extends t6{constructor(){super(...arguments),this.transcriptions=new sx(this._client),this.translations=new sS(this._client),this.speech=new sv(this._client)}}sA.Transcriptions=sx,sA.Translations=sS,sA.Speech=sv;class sI extends t6{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tW,{query:e,...t})}cancel(e,t){return this._client.post(t9`/batches/${e}/cancel`,t)}}class s$ extends t6{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/assistants/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/assistants/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tW,{query:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t9`/assistants/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sO extends t6{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sR extends t6{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sk extends t6{constructor(){super(...arguments),this.sessions=new sO(this._client),this.transcriptionSessions=new sR(this._client)}}sk.Sessions=sO,sk.TranscriptionSessions=sR;class sE extends t6{create(e,t,s){return this._client.post(t9`/threads/${e}/messages`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(t9`/threads/${r}/messages/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(t9`/threads/${r}/messages/${e}`,{body:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/threads/${e}/messages`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:r}=t;return this._client.delete(t9`/threads/${r}/messages/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class sP extends t6{retrieve(e,t,s){let{thread_id:r,run_id:n,...i}=t;return this._client.get(t9`/threads/${r}/runs/${n}/steps/${e}`,{query:i,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:r,...n}=t;return this._client.getAPIList(t9`/threads/${r}/runs/${e}/steps`,tW,{query:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sC=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,r=new Uint8Array(s);for(let e=0;e<s;e++)r[e]=t.charCodeAt(e);return Array.from(new Float32Array(r.buffer))}},sN=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sT extends ss{constructor(){super(...arguments),J.add(this),z.set(this,[]),V.set(this,{}),K.set(this,{}),Y.set(this,void 0),G.set(this,void 0),Q.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0),er.set(this,void 0),en.set(this,void 0)}[(z=new WeakMap,V=new WeakMap,K=new WeakMap,Y=new WeakMap,G=new WeakMap,Q=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,er=new WeakMap,en=new WeakMap,J=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new H;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let r=tC.fromReadableStream(e,this.controller);for await(let e of r)eM(this,J,"m",ei).call(this,e);if(r.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,J,"m",ea).call(this))}toReadableStream(){return new tC(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,r){let n=new H;return n._run(()=>n._runToolAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...r,signal:this.controller.signal});for await(let e of(this._connected(),a))eM(this,J,"m",ei).call(this,e);if(a.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,J,"m",ea).call(this))}static createThreadAssistantStream(e,t,s){let r=new H;return r._run(()=>r._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,s,r){let n=new H;return n._run(()=>n._runAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return eM(this,es,"f")}currentRun(){return eM(this,er,"f")}currentMessageSnapshot(){return eM(this,Y,"f")}currentRunStepSnapshot(){return eM(this,en,"f")}async finalRunSteps(){return await this.done(),Object.values(eM(this,V,"f"))}async finalMessages(){return await this.done(),Object.values(eM(this,K,"f"))}async finalRun(){if(await this.done(),!eM(this,G,"f"))throw Error("Final run was not received.");return eM(this,G,"f")}async _createThreadAssistantStream(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},i=await e.createAndRun(n,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))eM(this,J,"m",ei).call(this,e);if(i.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,J,"m",ea).call(this))}async _createAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...r,signal:this.controller.signal});for await(let e of(this._connected(),a))eM(this,J,"m",ei).call(this,e);if(a.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,J,"m",ea).call(this))}static accumulateDelta(e,t){for(let[s,r]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=r;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=r;continue}if("string"==typeof t&&"string"==typeof r)t+=r;else if("number"==typeof t&&"number"==typeof r)t+=r;else if(e5(t)&&e5(r))t=this.accumulateDelta(t,r);else if(Array.isArray(t)&&Array.isArray(r)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...r);continue}for(let e of r){if(!e5(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let r=t[s];null==r?t.push(e):t[s]=this.accumulateDelta(r,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${r}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,r){return await this._createAssistantStream(t,e,s,r)}async _runToolAssistantStream(e,t,s,r){return await this._createToolAssistantStream(t,e,s,r)}}H=sT,ei=function(e){if(!this.ended)switch(ej(this,es,e,"f"),eM(this,J,"m",ec).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eM(this,J,"m",ef).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eM(this,J,"m",el).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eM(this,J,"m",eo).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ea=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");if(!eM(this,G,"f"))throw Error("Final run has not been received");return eM(this,G,"f")},eo=function(e){let[t,s]=eM(this,J,"m",eh).call(this,e,eM(this,Y,"f"));for(let e of(ej(this,Y,t,"f"),eM(this,K,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,r=t.content[s.index];if(r&&"text"==r.type)this._emit("textDelta",e,r.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eM(this,Q,"f")){if(eM(this,Z,"f"))switch(eM(this,Z,"f").type){case"text":this._emit("textDone",eM(this,Z,"f").text,eM(this,Y,"f"));break;case"image_file":this._emit("imageFileDone",eM(this,Z,"f").image_file,eM(this,Y,"f"))}ej(this,Q,s.index,"f")}ej(this,Z,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eM(this,Q,"f")){let t=e.data.content[eM(this,Q,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eM(this,Y,"f"));break;case"text":this._emit("textDone",t.text,eM(this,Y,"f"))}}eM(this,Y,"f")&&this._emit("messageDone",e.data),ej(this,Y,void 0,"f")}},el=function(e){let t=eM(this,J,"m",eu).call(this,e);switch(ej(this,en,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eM(this,ee,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eM(this,et,"f")&&this._emit("toolCallDone",eM(this,et,"f")),ej(this,ee,e.index,"f"),ej(this,et,t.step_details.tool_calls[e.index],"f"),eM(this,et,"f")&&this._emit("toolCallCreated",eM(this,et,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ej(this,en,void 0,"f"),"tool_calls"==e.data.step_details.type&&eM(this,et,"f")&&(this._emit("toolCallDone",eM(this,et,"f")),ej(this,et,void 0,"f")),this._emit("runStepDone",e.data,t)}},ec=function(e){eM(this,z,"f").push(e),this._emit("event",e)},eu=function(e){switch(e.event){case"thread.run.step.created":return eM(this,V,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eM(this,V,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let r=H.accumulateDelta(t,s.delta);eM(this,V,"f")[e.data.id]=r}return eM(this,V,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eM(this,V,"f")[e.data.id]=e.data}if(eM(this,V,"f")[e.data.id])return eM(this,V,"f")[e.data.id];throw Error("No snapshot available")},eh=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(let e of r.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eM(this,J,"m",ed).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},ed=function(e,t){return H.accumulateDelta(t,e)},ef=function(e){switch(ej(this,er,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":ej(this,G,e.data,"f"),eM(this,et,"f")&&(this._emit("toolCallDone",eM(this,et,"f")),ej(this,et,void 0,"f"))}};class sj extends t6{constructor(){super(...arguments),this.steps=new sP(this._client)}create(e,t,s){let{include:r,...n}=t;return this._client.post(t9`/threads/${e}/runs`,{query:{include:r},body:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(t9`/threads/${r}/runs/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(t9`/threads/${r}/runs/${e}`,{body:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/threads/${e}/runs`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:r}=t;return this._client.post(t9`/threads/${r}/runs/${e}/cancel`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(r.id,{thread_id:e},s)}createAndStream(e,t,s){return sT.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let r=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...r}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,s){return sT.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:r,...n}=t;return this._client.post(t9`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let r=await this.submitToolOutputs(e,t,s);return await this.poll(r.id,t,s)}submitToolOutputsStream(e,t,s){return sT.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sj.Steps=sP;class sM extends t6{constructor(){super(...arguments),this.runs=new sj(this._client),this.messages=new sE(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/threads/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/threads/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(t9`/threads/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sT.createThreadAssistantStream(e,this._client.beta.threads,t)}}sM.Runs=sj,sM.Messages=sE;class sD extends t6{constructor(){super(...arguments),this.realtime=new sk(this._client),this.assistants=new s$(this._client),this.threads=new sM(this._client)}}sD.Realtime=sk,sD.Assistants=s$,sD.Threads=sM;class sL extends t6{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sU extends t6{retrieve(e,t,s){let{container_id:r}=t;return this._client.get(t9`/containers/${r}/files/${e}/content`,{...s,headers:sb([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sB extends t6{constructor(){super(...arguments),this.content=new sU(this._client)}create(e,t,s){return this._client.post(t9`/containers/${e}/files`,tz({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:r}=t;return this._client.get(t9`/containers/${r}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t9`/containers/${e}/files`,tW,{query:t,...s})}delete(e,t,s){let{container_id:r}=t;return this._client.delete(t9`/containers/${r}/files/${e}`,{...s,headers:sb([{Accept:"*/*"},s?.headers])})}}sB.Content=sU;class sq extends t6{constructor(){super(...arguments),this.files=new sB(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/containers/${e}`,{...t,headers:sb([{Accept:"*/*"},t?.headers])})}}sq.Files=sB;class sW extends t6{create(e,t){let s=!!e.encoding_format,r=s?e.encoding_format:"base64";s&&tE(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return s?n:(tE(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sC(t)}),e)))}}class sF extends t6{retrieve(e,t,s){let{eval_id:r,run_id:n}=t;return this._client.get(t9`/evals/${r}/runs/${n}/output_items/${e}`,s)}list(e,t,s){let{eval_id:r,...n}=t;return this._client.getAPIList(t9`/evals/${r}/runs/${e}/output_items`,tW,{query:n,...s})}}class sX extends t6{constructor(){super(...arguments),this.outputItems=new sF(this._client)}create(e,t,s){return this._client.post(t9`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:r}=t;return this._client.get(t9`/evals/${r}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t9`/evals/${e}/runs`,tW,{query:t,...s})}delete(e,t,s){let{eval_id:r}=t;return this._client.delete(t9`/evals/${r}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:r}=t;return this._client.post(t9`/evals/${r}/runs/${e}`,s)}}sX.OutputItems=sF;class sJ extends t6{constructor(){super(...arguments),this.runs=new sX(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/evals/${e}`,t)}update(e,t,s){return this._client.post(t9`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/evals/${e}`,t)}}sJ.Runs=sX;class sH extends t6{create(e,t){return this._client.post("/files",tz({body:e,...t},this._client))}retrieve(e,t){return this._client.get(t9`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/files/${e}`,t)}content(e,t){return this._client.get(t9`/files/${e}/content`,{...t,headers:sb([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let r=new Set(["processed","error","deleted"]),n=Date.now(),i=await this.retrieve(e);for(;!i.status||!r.has(i.status);)if(await e7(t),i=await this.retrieve(e),Date.now()-n>s)throw new eX({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sz extends t6{}class sV extends t6{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sK extends t6{constructor(){super(...arguments),this.graders=new sV(this._client)}}sK.Graders=sV;class sY extends t6{create(e,t,s){return this._client.getAPIList(t9`/fine_tuning/checkpoints/${e}/permissions`,tq,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(t9`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:r}=t;return this._client.delete(t9`/fine_tuning/checkpoints/${r}/permissions/${e}`,s)}}class sG extends t6{constructor(){super(...arguments),this.permissions=new sY(this._client)}}sG.Permissions=sY;class sQ extends t6{list(e,t={},s){return this._client.getAPIList(t9`/fine_tuning/jobs/${e}/checkpoints`,tW,{query:t,...s})}}class sZ extends t6{constructor(){super(...arguments),this.checkpoints=new sQ(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tW,{query:e,...t})}cancel(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(t9`/fine_tuning/jobs/${e}/events`,tW,{query:t,...s})}pause(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/resume`,t)}}sZ.Checkpoints=sQ;class s0 extends t6{constructor(){super(...arguments),this.methods=new sz(this._client),this.jobs=new sZ(this._client),this.checkpoints=new sG(this._client),this.alpha=new sK(this._client)}}s0.Methods=sz,s0.Jobs=sZ,s0.Checkpoints=sG,s0.Alpha=sK;class s1 extends t6{}class s2 extends t6{constructor(){super(...arguments),this.graderModels=new s1(this._client)}}s2.GraderModels=s1;class s3 extends t6{createVariation(e,t){return this._client.post("/images/variations",tz({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tz({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class s4 extends t6{retrieve(e,t){return this._client.get(t9`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tq,e)}delete(e,t){return this._client.delete(t9`/models/${e}`,t)}}class s6 extends t6{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function s5(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,r;return"output_text"===e.type?{...e,parsed:(s=t,r=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(r):JSON.parse(r))}:e});return{...e,content:s}}return e}),r=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||s8(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(let e of r.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),r}function s8(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s9 extends ss{constructor(e){super(),ep.add(this),em.set(this,void 0),eg.set(this,void 0),ey.set(this,void 0),ej(this,em,e,"f")}static createResponse(e,t,s){let r=new s9(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,s){let r,n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eM(this,ep,"m",ew).call(this);let i=null;for await(let n of("response_id"in t?(r=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):r=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),r))eM(this,ep,"m",e_).call(this,n,i);if(r.controller.signal?.aborted)throw new eW;return eM(this,ep,"m",eb).call(this)}[(em=new WeakMap,eg=new WeakMap,ey=new WeakMap,ep=new WeakSet,ew=function(){this.ended||ej(this,eg,void 0,"f")},e_=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},r=eM(this,ep,"m",ev).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=r.output[e.output_index];if(!t)throw new eB(`missing output at index ${e.output_index}`);if("message"===t.type){let r=t.content[e.content_index];if(!r)throw new eB(`missing content at index ${e.content_index}`);if("output_text"!==r.type)throw new eB(`expected content to be 'output_text', got ${r.type}`);s("response.output_text.delta",{...e,snapshot:r.text})}break}case"response.function_call_arguments.delta":{let t=r.output[e.output_index];if(!t)throw new eB(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},eb=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");let e=eM(this,eg,"f");if(!e)throw new eB("request ended without sending any events");ej(this,eg,void 0,"f");let t=function(e,t){var s;return t&&(s=t,sr(s.text?.format))?s5(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eM(this,em,"f"));return ej(this,ey,t,"f"),t},ev=function(e){let t=eM(this,eg,"f");if(!t){if("response.created"!==e.type)throw new eB(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ej(this,eg,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eB(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eB(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":ej(this,eg,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eM(this,ey,"f");if(!e)throw new eB("stream ended without producing a ChatCompletion");return e}}class s7 extends t6{list(e,t={},s){return this._client.getAPIList(t9`/responses/${e}/input_items`,tW,{query:t,...s})}}class re extends t6{constructor(){super(...arguments),this.inputItems=new s7(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s8(e),e))}retrieve(e,t={},s){return this._client.get(t9`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s8(e),e))}delete(e,t){return this._client.delete(t9`/responses/${e}`,{...t,headers:sb([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>s5(t,e))}stream(e,t){return s9.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t9`/responses/${e}/cancel`,t)}}re.InputItems=s7;class rt extends t6{create(e,t,s){return this._client.post(t9`/uploads/${e}/parts`,tz({body:t,...s},this._client))}}class rs extends t6{constructor(){super(...arguments),this.parts=new rt(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(t9`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(t9`/uploads/${e}/complete`,{body:t,...s})}}rs.Parts=rt;let rr=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let r=[];for(let e of t)"fulfilled"===e.status&&r.push(e.value);return r};class rn extends t6{create(e,t,s){return this._client.post(t9`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(t9`/vector_stores/${r}/file_batches/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:r}=t;return this._client.post(t9`/vector_stores/${r}/file_batches/${e}/cancel`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t);return await this.poll(e,r.id,s)}listFiles(e,t,s){let{vector_store_id:r,...n}=t;return this._client.getAPIList(t9`/vector_stores/${r}/file_batches/${e}/files`,tW,{query:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let r=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse();switch(n.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},r){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(r?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},r);o.push(e.id)}}let c=Array(n).fill(a).map(l);return await rr(c),await this.createAndPoll(e,{file_ids:o})}}class ri extends t6{create(e,t,s){return this._client.post(t9`/vector_stores/${e}/files`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(t9`/vector_stores/${r}/files/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:r,...n}=t;return this._client.post(t9`/vector_stores/${r}/files/${e}`,{body:n,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/vector_stores/${e}/files`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:r}=t;return this._client.delete(t9`/vector_stores/${r}/files/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(e,r.id,s)}async poll(e,t,s){let r=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse(),i=n.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let r=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:r.id},s)}async uploadAndPoll(e,t,s){let r=await this.upload(e,t,s);return await this.poll(e,r.id,s)}content(e,t,s){let{vector_store_id:r}=t;return this._client.getAPIList(t9`/vector_stores/${r}/files/${e}/content`,tq,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class ra extends t6{constructor(){super(...arguments),this.files=new ri(this._client),this.fileBatches=new rn(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/vector_stores/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/vector_stores/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tW,{query:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t9`/vector_stores/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(t9`/vector_stores/${e}/search`,tq,{body:t,method:"post",...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}ra.Files=ri,ra.FileBatches=rn;class ro extends t6{constructor(){super(...arguments),ex.add(this)}async unwrap(e,t,s=this._client.webhookSecret,r=300){return await this.verifySignature(e,t,s,r),JSON.parse(e)}async verifySignature(e,t,s=this._client.webhookSecret,r=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");eM(this,ex,"m",eS).call(this,s);let n=sb([t]).values,i=eM(this,ex,"m",eA).call(this,n,"webhook-signature"),a=eM(this,ex,"m",eA).call(this,n,"webhook-timestamp"),o=eM(this,ex,"m",eA).call(this,n,"webhook-id"),l=parseInt(a,10);if(isNaN(l))throw new e1("Invalid webhook timestamp format");let c=Math.floor(Date.now()/1e3);if(c-l>r)throw new e1("Webhook timestamp is too old");if(l>c+r)throw new e1("Webhook timestamp is too new");let u=i.split(" ").map(e=>e.startsWith("v1,")?e.substring(3):e),h=s.startsWith("whsec_")?Buffer.from(s.replace("whsec_",""),"base64"):Buffer.from(s,"utf-8"),d=o?`${o}.${a}.${e}`:`${a}.${e}`,f=await crypto.subtle.importKey("raw",h,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let e of u)try{let t=Buffer.from(e,"base64");if(await crypto.subtle.verify("HMAC",f,t,new TextEncoder().encode(d)))return}catch{continue}throw new e1("The given webhook signature does not match the expected signature")}}ex=new WeakSet,eS=function(e){if("string"!=typeof e||0===e.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},eA=function(e,t){if(!e)throw Error("Headers are required");let s=e.get(t);if(null==s)throw Error(`Missing required header: ${t}`);return s};class rl{constructor({baseURL:e=sN("OPENAI_BASE_URL"),apiKey:t=sN("OPENAI_API_KEY"),organization:s=sN("OPENAI_ORG_ID")??null,project:r=sN("OPENAI_PROJECT_ID")??null,webhookSecret:n=sN("OPENAI_WEBHOOK_SECRET")??null,...i}={}){if(eI.add(this),eO.set(this,void 0),this.completions=new sL(this),this.chat=new sw(this),this.embeddings=new sW(this),this.files=new sH(this),this.images=new s3(this),this.audio=new sA(this),this.moderations=new s6(this),this.models=new s4(this),this.fineTuning=new s0(this),this.graders=new s2(this),this.vectorStores=new ra(this),this.webhooks=new ro(this),this.beta=new sD(this),this.batches=new sI(this),this.uploads=new rs(this),this.responses=new re(this),this.evals=new sJ(this),this.containers=new sq(this),void 0===t)throw new eB("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:s,project:r,webhookSecret:n,...i,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&tt())throw new eB("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??e$.DEFAULT_TIMEOUT,this.logger=a.logger??console;let o="warn";this.logLevel=o,this.logLevel=tI(a.logLevel,"ClientOptions.logLevel",this)??tI(sN("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ej(this,eO,tu,"f"),this._options=a,this.apiKey=t,this.organization=s,this.project=r,this.webhookSecret=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}async authHeaders(e){return sb([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,r,n=e,i=function(e=t_){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||t_.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=th;if(void 0!==e.format){if(!tp(tf,e.format))throw TypeError("Unknown format option provided.");r=e.format}let n=tf[r],i=t_.filter;if(("function"==typeof e.filter||e4(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in ty?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":t_.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||t_.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:t_.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:t_.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:t_.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?t_.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:t_.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:t_.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:t_.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:t_.encodeValuesOnly,filter:i,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:t_.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:t_.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:t_.strictNullHandling}}(t);"function"==typeof i.filter?n=(0,i.filter)("",n):e4(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof n||null===n)return"";let o=ty[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(n)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===n[t]||tw(a,function e(t,s,r,n,i,a,o,l,c,u,h,d,f,p,m,g,y,w){var _,b;let v,x=t,S=w,A=0,I=!1;for(;void 0!==(S=S.get(tb))&&!I;){let e=S.get(t);if(A+=1,void 0!==e)if(e===A)throw RangeError("Cyclic object value");else I=!0;void 0===S.get(tb)&&(A=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=f?.(x):"comma"===r&&e4(x)&&(x=tg(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(a)return c&&!g?c(s,t_.encoder,y,"key",p):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,t_.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,t_.encoder,y,"value",p))]}return[m?.(s)+"="+m?.(String(x))]}let $=[];if(void 0===x)return $;if("comma"===r&&e4(x))g&&c&&(x=tg(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(e4(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let O=l?String(s).replace(/\./g,"%2E"):String(s),R=n&&e4(x)&&1===x.length?O+"[]":O;if(i&&e4(x)&&0===x.length)return R+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,I=e4(x)?"function"==typeof r?r(R,S):R:R+(d?"."+S:"["+S+"]");w.set(t,A);let O=new WeakMap;O.set(tb,w),tw($,e(b,I,r,n,i,a,o,l,"comma"===r&&g&&e4(x)?null:c,u,h,d,f,p,m,g,y,O))}return $}(n[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${te}`}defaultIdempotencyKey(){return`stainless-node-retry-${eD()}`}makeStatusError(e,t,s,r){return eq.generate(e,t,s,r)}buildURL(e,t,s){let r=!eM(this,eI,"m",eR).call(this)&&s||this.baseURL,n=new URL(e3(e)?e:r+(r.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(i)&&(t={...i,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(n.search=this.stringifyQuery(t)),n.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tL(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:i,url:a,timeout:o}=await this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(i,{url:a,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(tE(this).debug(`[${l}] sending request`,tP({retryOfRequestLogID:s,method:r.method,url:a,options:r,headers:i.headers})),r.signal?.aborted)throw new eW;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eU),f=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new eW;let n=eL(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return tE(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),tE(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,tP({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),this.retryRequest(r,t,s??l);if(tE(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),tE(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,tP({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),n)throw new eX;throw new eF({cause:d})}let p=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${p}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${f-u}ms`;if(!d.ok){let e=await this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tc(d.body),tE(this).info(`${m} - ${e}`),tE(this).debug(`[${l}] response error (${e})`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),this.retryRequest(r,t,s??l,d.headers)}let n=e?"error; no more retries left":"error; not retryable";tE(this).info(`${m} - ${n}`);let i=await d.text().catch(e=>eU(e).message),a=e9(i),o=a?void 0:i;throw tE(this).debug(`[${l}] response error (${n})`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return tE(this).info(m),tE(this).debug(`[${l}] response start`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),{response:d,options:r,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tB(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:i,...a}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}async shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,i=r?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(n=e)}let a=r?.get("retry-after");if(a&&!n){let e=parseFloat(a);n=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await e7(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}async buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:i,defaultBaseURL:a}=s,o=this.buildURL(n,i,a);"timeout"in s&&e8("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:l,body:c}=this.buildBody({options:s}),u=await this.buildHeaders({options:e,method:r,bodyHeaders:l,retryCount:t});return{req:{method:r,headers:u,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&c instanceof globalThis.ReadableStream&&{duplex:"half"},...c&&{body:c},...this.fetchOptions??{},...s.fetchOptions??{}},url:o,timeout:s.timeout}}async buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let i=sb([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...ti(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sb([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:to(e)}:eM(this,eO,"f").call(this,{body:e,headers:s})}}e$=rl,eO=new WeakMap,eI=new WeakSet,eR=function(){return"https://api.openai.com/v1"!==this.baseURL},rl.OpenAI=e$,rl.DEFAULT_TIMEOUT=6e5,rl.OpenAIError=eB,rl.APIError=eq,rl.APIConnectionError=eF,rl.APIConnectionTimeoutError=eX,rl.APIUserAbortError=eW,rl.NotFoundError=eV,rl.ConflictError=eK,rl.RateLimitError=eG,rl.BadRequestError=eJ,rl.AuthenticationError=eH,rl.InternalServerError=eQ,rl.PermissionDeniedError=ez,rl.UnprocessableEntityError=eY,rl.InvalidWebhookSignatureError=e1,rl.toFile=t3,rl.Completions=sL,rl.Chat=sw,rl.Embeddings=sW,rl.Files=sH,rl.Images=s3,rl.Audio=sA,rl.Moderations=s6,rl.Models=s4,rl.FineTuning=s0,rl.Graders=s2,rl.VectorStores=ra,rl.Webhooks=ro,rl.Beta=sD,rl.Batches=sI,rl.Uploads=rs,rl.Responses=re,rl.Evals=sJ,rl.Containers=sq;let rc=new rl({apiKey:process.env.OPENAI_API_KEY});async function ru(e){try{let t=await rc.chat.completions.create({model:"gpt-4o-mini",messages:[{role:"user",content:[{type:"text",text:`Analyze this image and estimate the age of the person in it. 
              
              Please respond with a JSON object containing:
              - estimatedAge: number (the estimated age in years, or null if no person detected)
              - confidence: number (confidence level from 0 to 1)
              - hasPersonDetected: boolean (true if a person is clearly visible)
              - explanation: string (brief explanation of your analysis)
              
              If no person is detected or the image is unclear, set estimatedAge to null and hasPersonDetected to false.
              Be as accurate as possible with age estimation, considering facial features, skin texture, and other age indicators.
              
              Example response:
              {
                "estimatedAge": 25,
                "confidence": 0.8,
                "hasPersonDetected": true,
                "explanation": "Clear facial features visible, appears to be a young adult based on skin texture and facial structure"
              }`},{type:"image_url",image_url:{url:`data:image/jpeg;base64,${e}`,detail:"low"}}]}],max_tokens:300,temperature:.1}),s=t.choices[0]?.message?.content;if(!s)throw Error("No response from OpenAI");try{let e=JSON.parse(s);if("boolean"!=typeof e.hasPersonDetected)throw Error("Invalid response format");return{estimatedAge:e.estimatedAge,confidence:Math.max(0,Math.min(1,e.confidence||0)),hasPersonDetected:e.hasPersonDetected,explanation:e.explanation||"No explanation provided"}}catch(n){let e=s.toLowerCase().includes("person")||s.toLowerCase().includes("face")||s.toLowerCase().includes("human"),t=s.match(/(\d+)\s*years?\s*old/i)||s.match(/age.*?(\d+)/i)||s.match(/(\d+).*?age/i),r=t?parseInt(t[1]):null;return{estimatedAge:e?r:null,confidence:.5*!!e,hasPersonDetected:e,explanation:s.substring(0,200)+(s.length>200?"...":"")}}}catch(e){return console.error("OpenAI API error:",e),{estimatedAge:null,confidence:0,hasPersonDetected:!1,explanation:"Error analyzing image",error:e instanceof Error?e.message:"Unknown error"}}}var rh=s(2049),rd=s(974);async function rf(e){try{let t=(0,eT.d)(),{data:{user:s}}=await t.auth.getUser(),r=s?.id,n=(0,rd.Tf)(e),i=await (0,rh.hS)(r,n);if(!i.canUse)return eN.NextResponse.json({error:i.needsCredits?"You have reached your daily limit. Purchase credits to continue.":"Daily limit reached. Please try again tomorrow or create an account for more uses.",needsCredits:i.needsCredits,isAnonymous:i.isAnonymous},{status:429});let a=(await e.formData()).get("image");if(!a)return eN.NextResponse.json({error:"No image file provided"},{status:400});let o=["image/jpeg","image/jpg","image/png","image/webp"].includes(a.type)?a.size>0xa00000?{valid:!1,error:"Image file is too large. Please upload an image smaller than 10MB"}:{valid:!0}:{valid:!1,error:"Please upload a valid image file (JPEG, PNG, or WebP)"};if(!o.valid)return eN.NextResponse.json({error:o.error},{status:400});let l=await a.arrayBuffer(),c=Buffer.from(l).toString("base64"),u=await ru(c);if(u.error)return eN.NextResponse.json({error:u.error},{status:500});if(!await (0,rh.SQ)(r,n))return eN.NextResponse.json({error:"Failed to process request. Please try again."},{status:500});let h={user_id:r,image_url:null,estimated_age:u.estimatedAge,confidence_score:u.confidence,analysis_result:{hasPersonDetected:u.hasPersonDetected,explanation:u.explanation,timestamp:new Date().toISOString()}},{data:d,error:f}=await t.from("photo_analyses").insert(h).select().single();f&&console.error("Error saving analysis:",f);let p=await (0,rh.hS)(r,n);return eN.NextResponse.json({success:!0,analysis:{estimatedAge:u.estimatedAge,confidence:u.confidence,hasPersonDetected:u.hasPersonDetected,explanation:u.explanation},usage:{remainingUses:p.remainingUses,isAnonymous:p.isAnonymous,needsCredits:p.needsCredits},analysisId:d?.id})}catch(e){return console.error("Analysis API error:",e),eN.NextResponse.json({error:"Internal server error"},{status:500})}}let rp=new eE.AppRouteRouteModule({definition:{kind:eP.RouteKind.APP_ROUTE,page:"/api/analyze/route",pathname:"/api/analyze",filename:"route",bundlePath:"app/api/analyze/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts",nextConfigOutput:"",userland:ek}),{workAsyncStorage:rm,workUnitAsyncStorage:rg,serverHooks:ry}=rp;function rw(){return(0,eC.patchFetch)({workAsyncStorage:rm,workUnitAsyncStorage:rg})}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,410],()=>s(7437));module.exports=r})();
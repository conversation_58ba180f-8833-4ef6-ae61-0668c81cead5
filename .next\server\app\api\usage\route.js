(()=>{var e={};e.id=347,e.ids=[347],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,s,t)=>{"use strict";function r(e){let s=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip"),r=e.headers.get("cf-connecting-ip");return r||t||(s?s.split(",")[0].trim():"127.0.0.1")}t.d(s,{Tf:()=>r})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,s,t)=>{"use strict";t.d(s,{SQ:()=>n,hS:()=>a,sy:()=>u});var r=t(3769),i=t(6621);async function a(e,s){let t=(0,r.d)();if(e){let{data:s}=await t.from("user_profiles").select("*").eq("id",e).single();if(!s)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let r=new Date().toISOString().split("T")[0],i=s.last_use_date,a=s.daily_uses;return(i!==r&&(a=0,await t.from("user_profiles").update({daily_uses:0,last_use_date:r}).eq("id",e)),a<3)?{canUse:!0,remainingUses:3-a,isAnonymous:!1,needsCredits:!1}:s.credits>0?{canUse:!0,remainingUses:s.credits,isAnonymous:!1,needsCredits:!1}:{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!0}}{if(!s)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],t=(0,i.vZ)(),{data:r}=await t.from("anonymous_uses").select("*").eq("ip_address",s).eq("use_date",e),a=1>(r?.length||0);return{canUse:a,remainingUses:+!!a,isAnonymous:!0,needsCredits:!1}}}async function n(e,s){let t=(0,r.d)();if(e){let{data:s}=await t.from("user_profiles").select("*").eq("id",e).single();if(!s)return!1;let r=new Date().toISOString().split("T")[0],i=s.daily_uses;return(s.last_use_date!==r&&(i=0),i<3)?(await t.from("user_profiles").update({daily_uses:i+1,last_use_date:r,updated_at:new Date().toISOString()}).eq("id",e),!0):s.credits>0&&(await t.from("user_profiles").update({credits:s.credits-1,updated_at:new Date().toISOString()}).eq("id",e),!0)}{if(!s)return!1;let e=new Date().toISOString().split("T")[0],t=(0,i.vZ)(),{data:r}=await t.from("anonymous_uses").select("*").eq("ip_address",s).eq("use_date",e).single();return!r&&(await t.from("anonymous_uses").insert({ip_address:s,use_date:e}),!0)}}async function u(e,s){let t=(0,r.d)(),{data:i}=await t.from("user_profiles").select("credits").eq("id",e).single();return!!i&&(await t.from("user_profiles").update({credits:i.credits+s,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,s,t)=>{"use strict";t.d(s,{d:()=>a});var r=t(4386),i=t(4999);let a=()=>{let e=(0,i.UL)();return(0,r.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:s=>e.get(s)?.value,set(s,t,r){e.set({name:s,value:t,...r})},remove(s,t){e.set({name:s,value:"",...t})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{GET:()=>c});var i=t(6559),a=t(8088),n=t(7719),u=t(2190),o=t(3769),d=t(2049),p=t(974);async function c(e){try{let s=(0,o.d)(),{data:{user:t}}=await s.auth.getUser(),r=t?.id,i=(0,p.Tf)(e),a=await (0,d.hS)(r,i),n=null;if(r){let{data:e}=await s.from("user_profiles").select("*").eq("id",r).single();n=e}return u.NextResponse.json({canUse:a.canUse,remainingUses:a.remainingUses,isAnonymous:a.isAnonymous,needsCredits:a.needsCredits,user:t?{id:t.id,email:t.email,profile:n}:null})}catch(e){return console.error("Usage API error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/usage/route",pathname:"/api/usage",filename:"route",bundlePath:"app/api/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},6621:(e,s,t)=>{"use strict";t.d(s,{vZ:()=>u});var r=t(6437);t(4386);let i=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",a=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",n=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",u=()=>{let e=i(),s=n();if(!e||!s)throw Error("Supabase URL and Service Role Key are required");return(0,r.UU)(e,s,{auth:{autoRefreshToken:!1,persistSession:!1}})};(()=>{let e=i(),s=a();if(!e||!s)throw Error("Supabase URL and Anon Key are required");return(0,r.UU)(e,s)})(),u()},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,580,410],()=>t(6549));module.exports=r})();
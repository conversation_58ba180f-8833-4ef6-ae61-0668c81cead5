(()=>{var e={};e.id=24,e.ids=[24],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>n,hS:()=>a,sy:()=>o});var s=r(3769),i=r(6621);async function a(e,t){let r=(0,s.d)();if(e){let{data:t}=await r.from("user_profiles").select("*").eq("id",e).single();if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let s=new Date().toISOString().split("T")[0],i=t.last_use_date,a=t.daily_uses;return(i!==s&&(a=0,await r.from("user_profiles").update({daily_uses:0,last_use_date:s}).eq("id",e)),a<3)?{canUse:!0,remainingUses:3-a,isAnonymous:!1,needsCredits:!1}:t.credits>0?{canUse:!0,remainingUses:t.credits,isAnonymous:!1,needsCredits:!1}:{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!0}}{if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],r=(0,i.vZ)(),{data:s}=await r.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e),a=1>(s?.length||0);return{canUse:a,remainingUses:+!!a,isAnonymous:!0,needsCredits:!1}}}async function n(e,t){let r=(0,s.d)();if(e){let{data:t}=await r.from("user_profiles").select("*").eq("id",e).single();if(!t)return!1;let s=new Date().toISOString().split("T")[0],i=t.daily_uses;return(t.last_use_date!==s&&(i=0),i<3)?(await r.from("user_profiles").update({daily_uses:i+1,last_use_date:s,updated_at:new Date().toISOString()}).eq("id",e),!0):t.credits>0&&(await r.from("user_profiles").update({credits:t.credits-1,updated_at:new Date().toISOString()}).eq("id",e),!0)}{if(!t)return!1;let e=new Date().toISOString().split("T")[0],r=(0,i.vZ)(),{data:s}=await r.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e).single();return!s&&(await r.from("anonymous_uses").insert({ip_address:t,use_date:e}),!0)}}async function o(e,t){let r=(0,s.d)(),{data:i}=await r.from("user_profiles").select("credits").eq("id",e).single();return!!i&&(await r.from("user_profiles").update({credits:i.credits+t,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,r)=>{"use strict";r.d(t,{d:()=>a});var s=r(4386),i=r(4999);let a=()=>{let e=(0,i.UL)();return(0,s.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){e.set({name:t,value:r,...s})},remove(t,r){e.set({name:t,value:"",...r})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6621:(e,t,r)=>{"use strict";r.d(t,{vZ:()=>o});var s=r(6437);r(4386);let i=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",a=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",n=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",o=()=>{let e=i(),t=n();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,s.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})};(()=>{let e=i(),t=a();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,s.UU)(e,t)})(),o()},6706:(e,t,r)=>{"use strict";r.d(t,{C:()=>i,f:()=>a,t6:()=>n});let s=new(r(7877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia"}),i={basic:{name:"5 Credits",credits:5,price:500,description:"Perfect for occasional use"}};async function a(e,t,r){let a=i[t],n=await s.paymentIntents.create({amount:a.price,currency:"usd",metadata:{userId:e,packageType:t,credits:a.credits.toString()},receipt_email:r,description:`${a.name} - Age Analysis Credits`});return{clientSecret:n.client_secret,paymentIntentId:n.id}}function n(e,t){try{return s.webhooks.constructEvent(e,t,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return console.error("Webhook signature verification failed:",e),null}}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9727:()=>{},9871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>_,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{POST:()=>l});var i=r(6559),a=r(8088),n=r(7719),o=r(2190),u=r(4999),d=r(6706),c=r(6621),p=r(2049);async function l(e){try{let t=await e.text(),r=(0,u.b3)().get("stripe-signature");if(!r)return o.NextResponse.json({error:"No signature"},{status:400});let s=(0,d.t6)(t,r);if(!s)return o.NextResponse.json({error:"Invalid signature"},{status:400});if("payment_intent.succeeded"===s.type){let e=s.data.object,t=e.metadata.userId,r=parseInt(e.metadata.credits||"0");if(!t||!r)return console.error("Missing metadata in payment intent:",e.id),o.NextResponse.json({error:"Invalid metadata"},{status:400});let i=(0,c.vZ)();if(await i.from("credit_transactions").update({status:"completed"}).eq("stripe_payment_intent_id",e.id),!await (0,p.sy)(t,r))return console.error("Failed to add credits for user:",t),o.NextResponse.json({error:"Failed to add credits"},{status:500});console.log(`Added ${r} credits to user ${t}`)}if("payment_intent.payment_failed"===s.type){let e=s.data.object,t=(0,c.vZ)();await t.from("credit_transactions").update({status:"failed"}).eq("stripe_payment_intent_id",e.id)}return o.NextResponse.json({received:!0})}catch(e){return console.error("Stripe webhook error:",e),o.NextResponse.json({error:"Webhook handler failed"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:_}=m;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,410,877],()=>r(9871));module.exports=s})();
(()=>{var e={};e.id=24,e.ids=[24],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>n,hS:()=>a,sy:()=>o});var r=s(3769),i=s(6621);async function a(e,t){let s=await (0,r.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let r=new Date().toISOString().split("T")[0],i=t.last_use_date,a=t.daily_uses;return(i!==r&&(a=0,await s.from("user_profiles").update({daily_uses:0,last_use_date:r}).eq("id",e)),a<3)?{canUse:!0,remainingUses:3-a,isAnonymous:!1,needsCredits:!1}:t.credits>0?{canUse:!0,remainingUses:t.credits,isAnonymous:!1,needsCredits:!1}:{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!0}}{if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],s=(0,i.vZ)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e),a=1>(r?.length||0);return{canUse:a,remainingUses:+!!a,isAnonymous:!0,needsCredits:!1}}}async function n(e,t){let s=await (0,r.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return!1;let r=new Date().toISOString().split("T")[0],i=t.daily_uses;return(t.last_use_date!==r&&(i=0),i<3)?(await s.from("user_profiles").update({daily_uses:i+1,last_use_date:r,updated_at:new Date().toISOString()}).eq("id",e),!0):t.credits>0&&(await s.from("user_profiles").update({credits:t.credits-1,updated_at:new Date().toISOString()}).eq("id",e),!0)}{if(!t)return!1;let e=new Date().toISOString().split("T")[0],s=(0,i.vZ)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e).single();return!r&&(await s.from("anonymous_uses").insert({ip_address:t,use_date:e}),!0)}}async function o(e,t){let s=await (0,r.d)(),{data:i}=await s.from("user_profiles").select("credits").eq("id",e).single();return!!i&&(await s.from("user_profiles").update({credits:i.credits+t,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(4386),i=s(4999);let a=async()=>{let e=await (0,i.UL)();return(0,r.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){e.set({name:t,value:s,...r})},remove(t,s){e.set({name:t,value:"",...s})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6621:(e,t,s)=>{"use strict";s.d(t,{vZ:()=>o});var r=s(6437);s(4386);let i=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",a=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",n=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",o=()=>{let e=i(),t=n();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,r.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})};(()=>{let e=i(),t=a();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,r.UU)(e,t)})(),o()},6706:(e,t,s)=>{"use strict";s.d(t,{C:()=>i,f:()=>a,t6:()=>n});let r=new(s(7877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil"}),i={basic:{name:"5 Credits",credits:5,price:500,description:"Perfect for occasional use"}};async function a(e,t,s){let a=i[t],n=await r.paymentIntents.create({amount:a.price,currency:"usd",metadata:{userId:e,packageType:t,credits:a.credits.toString()},receipt_email:s,description:`${a.name} - Age Analysis Credits`});return{clientSecret:n.client_secret,paymentIntentId:n.id}}function n(e,t){try{return r.webhooks.constructEvent(e,t,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return console.error("Webhook signature verification failed:",e),null}}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9727:()=>{},9871:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var r={};s.r(r),s.d(r,{POST:()=>p});var i=s(6559),a=s(8088),n=s(7719),o=s(2190),u=s(6706),d=s(6621),c=s(2049);async function p(e){try{let t=await e.text(),s=e.headers.get("stripe-signature");if(!s)return o.NextResponse.json({error:"No signature"},{status:400});let r=(0,u.t6)(t,s);if(!r)return o.NextResponse.json({error:"Invalid signature"},{status:400});if("payment_intent.succeeded"===r.type){let e=r.data.object,t=e.metadata.userId,s=parseInt(e.metadata.credits||"0");if(!t||!s)return console.error("Missing metadata in payment intent:",e.id),o.NextResponse.json({error:"Invalid metadata"},{status:400});let i=(0,d.vZ)();if(await i.from("credit_transactions").update({status:"completed"}).eq("stripe_payment_intent_id",e.id),!await (0,c.sy)(t,s))return console.error("Failed to add credits for user:",t),o.NextResponse.json({error:"Failed to add credits"},{status:500});console.log(`Added ${s} credits to user ${t}`)}if("payment_intent.payment_failed"===r.type){let e=r.data.object,t=(0,d.vZ)();await t.from("credit_transactions").update({status:"failed"}).eq("stripe_payment_intent_id",e.id)}return o.NextResponse.json({received:!0})}catch(e){return console.error("Stripe webhook error:",e),o.NextResponse.json({error:"Webhook handler failed"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:f}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,410,877],()=>s(9871));module.exports=r})();
(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},484:(e,t,r)=>{Promise.resolve().then(r.bind(r,1220))},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx","default")},1220:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rI});var n,i,a,o,s,l,u=r(687),c=r(3210),d=r.t(c,2);let f=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),p=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),m=e=>{let t=p(e);return t.charAt(0).toUpperCase()+t.slice(1)},h=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),g=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var x={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let y=(0,c.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:i="",children:a,iconNode:o,...s},l)=>(0,c.createElement)("svg",{ref:l,...x,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:h("lucide",i),...!a&&!g(s)&&{"aria-hidden":"true"},...s},[...o.map(([e,t])=>(0,c.createElement)(e,t)),...Array.isArray(a)?a:[a]])),v=(e,t)=>{let r=(0,c.forwardRef)(({className:r,...n},i)=>(0,c.createElement)(y,{ref:i,iconNode:t,className:h(`lucide-${f(m(e))}`,`lucide-${e}`,r),...n}));return r.displayName=m(e),r},b=v("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),j=v("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),E=v("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var w=r(6558);let N=v("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),P=v("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),S=v("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),A=v("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),R=v("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function C({onNeedAuth:e,onNeedCredits:t}){let[r,n]=(0,c.useState)(null),[i,a]=(0,c.useState)(null),[o,s]=(0,c.useState)(!1),[l,d]=(0,c.useState)(null),[f,p]=(0,c.useState)(null),[m,h]=(0,c.useState)(""),g=(0,c.useRef)(null),{user:x,profile:y}=(0,w.A)(),v=e=>{n(e),d(null),h("");let t=new FileReader;t.onload=e=>{a(e.target?.result)},t.readAsDataURL(e)},j=async()=>{if(r){s(!0),h("");try{let n=new FormData;n.append("image",r);let i=await fetch("/api/analyze",{method:"POST",body:n}),a=await i.json();if(!i.ok)throw 429===i.status&&(a.needsCredits?t():a.isAnonymous&&e()),Error(a.error||"Analysis failed");d(a.analysis),p(a.usage)}catch(e){h(e instanceof Error?e.message:"An error occurred")}finally{s(!1)}}};return(0,u.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,u.jsx)("div",{onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files;t.length>0&&v(t[0])},onDragOver:e=>e.preventDefault(),className:`border-2 border-dashed rounded-2xl p-8 text-center transition-colors ${i?"border-blue-300 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"}`,children:i?(0,u.jsxs)("div",{className:"space-y-4",children:[(0,u.jsx)("img",{src:i,alt:"Preview",className:"max-w-full max-h-64 mx-auto rounded-lg shadow-md"}),(0,u.jsxs)("div",{className:"flex justify-center space-x-3",children:[(0,u.jsx)("button",{onClick:j,disabled:o,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:o?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),(0,u.jsx)("span",{children:"Analyzing..."})]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(b,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Analyze Age"})]})}),(0,u.jsx)("button",{onClick:()=>{n(null),a(null),d(null),h(""),g.current&&(g.current.value="")},className:"bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Choose Different Photo"})]})]}):(0,u.jsxs)("div",{className:"space-y-4",children:[(0,u.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto",children:(0,u.jsx)(N,{className:"w-8 h-8 text-blue-600"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload a Photo"}),(0,u.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop your photo here, or click to select"}),(0,u.jsx)("button",{onClick:()=>g.current?.click(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Select Photo"})]}),(0,u.jsx)("input",{ref:g,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files;t&&t.length>0&&v(t[0])},className:"hidden"})]})}),m&&(0,u.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[(0,u.jsx)(P,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h4",{className:"font-medium text-red-900",children:"Error"}),(0,u.jsx)("p",{className:"text-red-700 text-sm",children:m})]})]}),l&&(0,u.jsxs)("div",{className:"bg-white border border-gray-200 rounded-2xl p-6 shadow-sm",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,u.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(S,{className:"w-5 h-5 text-green-600"})}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Complete"})]}),l.hasPersonDetected?(0,u.jsxs)("div",{className:"space-y-4",children:[(0,u.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,u.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,u.jsx)(A,{className:"w-4 h-4 text-blue-600"}),(0,u.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Estimated Age"})]}),(0,u.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[l.estimatedAge," years"]})]}),(0,u.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,u.jsx)(R,{className:"w-4 h-4 text-green-600"}),(0,u.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Confidence"})]}),(0,u.jsxs)("div",{className:"text-2xl font-bold text-green-900",children:[Math.round(100*l.confidence),"%"]})]})]}),(0,u.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,u.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Analysis Details"}),(0,u.jsx)("p",{className:"text-gray-700 text-sm",children:l.explanation})]})]}):(0,u.jsxs)("div",{className:"text-center py-4",children:[(0,u.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,u.jsx)(P,{className:"w-6 h-6 text-yellow-600"})}),(0,u.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"No Person Detected"}),(0,u.jsx)("p",{className:"text-gray-600 text-sm",children:l.explanation})]})]}),f&&(0,u.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h4",{className:"font-medium text-blue-900",children:f.isAnonymous?"Daily Limit":"Remaining Uses"}),(0,u.jsx)("p",{className:"text-sm text-blue-700",children:f.isAnonymous?`${f.remainingUses} free analysis remaining today`:`${f.remainingUses} analyses remaining`})]}),f.needsCredits&&(0,u.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Buy Credits"})]})})]})}let k=v("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),O=v("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function _(){let[e,t]=(0,c.useState)([]),[r,n]=(0,c.useState)(!0),[i,a]=(0,c.useState)(""),[o,s]=(0,c.useState)(1),[l,d]=(0,c.useState)(1),{user:f}=(0,w.A)(),p=async(e=1)=>{if(f){n(!0),a("");try{let r=await fetch(`/api/history?page=${e}&limit=10`);if(!r.ok)throw Error("Failed to fetch history");let n=await r.json();t(n.analyses),s(n.pagination.page),d(n.pagination.totalPages)}catch(e){a(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}}},m=e=>{p(e)};return f?r?(0,u.jsxs)("div",{className:"text-center py-12",children:[(0,u.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"}),(0,u.jsx)("p",{className:"text-gray-600",children:"Loading your history..."})]}):i?(0,u.jsxs)("div",{className:"text-center py-12",children:[(0,u.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,u.jsx)(P,{className:"w-8 h-8 text-red-500"})}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error Loading History"}),(0,u.jsx)("p",{className:"text-gray-600 mb-4",children:i}),(0,u.jsx)("button",{onClick:()=>p(o),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Try Again"})]}):0===e.length?(0,u.jsxs)("div",{className:"text-center py-12",children:[(0,u.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,u.jsx)(E,{className:"w-8 h-8 text-gray-400"})}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Analysis History"}),(0,u.jsx)("p",{className:"text-gray-600",children:"Your age analysis history will appear here after you upload photos."})]}):(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,u.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(E,{className:"w-4 h-4 text-blue-600"})}),(0,u.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Analysis History"})]}),(0,u.jsx)("div",{className:"space-y-4",children:e.map(e=>{var t;return(0,u.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,u.jsx)("div",{className:"flex items-start justify-between",children:(0,u.jsxs)("div",{className:"flex-1",children:[(0,u.jsx)("div",{className:"flex items-center space-x-3 mb-2",children:e.estimated_age?(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(A,{className:"w-4 h-4 text-blue-600"})}),(0,u.jsxs)("div",{children:[(0,u.jsxs)("span",{className:"font-semibold text-gray-900",children:[e.estimated_age," years old"]}),(0,u.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",Math.round(100*(e.confidence_score||0)),"% confidence)"]})]})]}):(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(P,{className:"w-4 h-4 text-yellow-600"})}),(0,u.jsx)("span",{className:"font-medium text-gray-900",children:"No person detected"})]})}),e.analysis_result?.explanation&&(0,u.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.analysis_result.explanation}),(0,u.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,u.jsx)(R,{className:"w-3 h-3"}),(0,u.jsx)("span",{children:(t=e.created_at,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(t)))})]})]})})},e.id)})}),l>1&&(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("button",{onClick:()=>m(o-1),disabled:o<=1,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,u.jsx)(k,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Previous"})]}),(0,u.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",o," of ",l]}),(0,u.jsxs)("button",{onClick:()=>m(o+1),disabled:o>=l,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,u.jsx)("span",{children:"Next"}),(0,u.jsx)(O,{className:"w-4 h-4"})]})]})]}):(0,u.jsxs)("div",{className:"text-center py-12",children:[(0,u.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,u.jsx)(E,{className:"w-8 h-8 text-gray-400"})}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sign In to View History"}),(0,u.jsx)("p",{className:"text-gray-600",children:"Create an account to keep track of your age analyses."})]})}let T=v("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),M=v("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),I=v("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);function F({onBuyCredits:e}){var t;let{user:r,profile:n,signOut:i}=(0,w.A)(),[a,o]=(0,c.useState)(!1);if(!r||!n)return null;let s=Math.max(0,3-n.daily_uses),l=n.last_use_date!==new Date().toISOString().split("T")[0];return(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsxs)("button",{onClick:()=>o(!a),className:"flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors",children:[(0,u.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(A,{className:"w-4 h-4 text-blue-600"})}),(0,u.jsxs)("div",{className:"text-left",children:[(0,u.jsx)("div",{className:"text-sm font-medium text-gray-900",children:r.email?.split("@")[0]}),(0,u.jsx)("div",{className:"text-xs text-gray-500",children:1===(t=n.credits)?"1 credit":`${t} credits`})]})]}),a&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>o(!1)}),(0,u.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:[(0,u.jsx)("div",{className:"p-4 border-b border-gray-100",children:(0,u.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,u.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,u.jsx)(A,{className:"w-5 h-5 text-blue-600"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"font-medium text-gray-900",children:r.email}),(0,u.jsxs)("div",{className:"text-sm text-gray-500",children:["Member since ",new Date(n.created_at).toLocaleDateString()]})]})]})}),(0,u.jsxs)("div",{className:"p-4 space-y-4",children:[(0,u.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,u.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(T,{className:"w-4 h-4 text-blue-600"}),(0,u.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Daily Uses"})]}),(0,u.jsxs)("div",{className:"text-lg font-bold text-blue-900 mt-1",children:[l?3:s,"/3"]}),(0,u.jsx)("div",{className:"text-xs text-blue-700",children:l?"Refreshed today":"Remaining today"})]}),(0,u.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(M,{className:"w-4 h-4 text-green-600"}),(0,u.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Credits"})]}),(0,u.jsx)("div",{className:"text-lg font-bold text-green-900 mt-1",children:n.credits}),(0,u.jsx)("div",{className:"text-xs text-green-700",children:"Extra analyses"})]})]}),(0,u.jsxs)("button",{onClick:()=>{e(),o(!1)},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2",children:[(0,u.jsx)(M,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Buy More Credits"})]}),(0,u.jsxs)("button",{onClick:()=>{i(),o(!1)},className:"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2",children:[(0,u.jsx)(I,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Sign Out"})]})]})]})]})]})}var D=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(D||{}),L=Object.defineProperty,U=(e,t,r)=>t in e?L(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,$=(e,t,r)=>(U(e,"symbol"!=typeof t?t+"":t,r),r);class q{constructor(){$(this,"current",this.detect()),$(this,"handoffState","pending"),$(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let W=new q,z=(e,t)=>{W.isServer?(0,c.useEffect)(e,t):(0,c.useLayoutEffect)(e,t)};function H(e){let t=(0,c.useRef)(e);return z(()=>{t.current=e},[e]),t}class B extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}function X(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function V(){let e=[],t={addEventListener:(e,r,n,i)=>(e.addEventListener(r,n,i),t.add(()=>e.removeEventListener(r,n,i))),requestAnimationFrame(...e){let r=requestAnimationFrame(...e);return t.add(()=>cancelAnimationFrame(r))},nextFrame:(...e)=>t.requestAnimationFrame(()=>t.requestAnimationFrame(...e)),setTimeout(...e){let r=setTimeout(...e);return t.add(()=>clearTimeout(r))},microTask(...e){let r={current:!0};return X(()=>{r.current&&e[0]()}),t.add(()=>{r.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=V();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let t of e.splice(r,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}var G=Object.defineProperty,Y=(e,t,r)=>t in e?G(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,K=(e,t,r)=>(Y(e,"symbol"!=typeof t?t+"":t,r),r),Q=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)},Z=(e,t,r)=>(Q(e,t,"read from private field"),r?r.call(e):t.get(e)),J=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},ee=(e,t,r,n)=>(Q(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);class et{constructor(e){J(this,n,{}),J(this,i,new B(()=>new Set)),J(this,a,new Set),K(this,"disposables",V()),ee(this,n,e)}dispose(){this.disposables.dispose()}get state(){return Z(this,n)}subscribe(e,t){let r={selector:e,callback:t,current:e(Z(this,n))};return Z(this,a).add(r),this.disposables.add(()=>{Z(this,a).delete(r)})}on(e,t){return Z(this,i).get(e).add(t),this.disposables.add(()=>{Z(this,i).get(e).delete(t)})}send(e){let t=this.reduce(Z(this,n),e);if(t!==Z(this,n)){for(let e of(ee(this,n,t),Z(this,a))){let t=e.selector(Z(this,n));er(e.current,t)||(e.current=t,e.callback(t))}for(let t of Z(this,i).get(e.type))t(Z(this,n),e)}}}function er(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&en(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&en(e.entries(),t.entries()):!!(ei(e)&&ei(t))&&en(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function en(e,t){for(;;){let r=e.next(),n=t.next();if(r.done&&n.done)return!0;if(r.done||n.done||!Object.is(r.value,n.value))return!1}}function ei(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function ea(e,t,...r){if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let n=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,ea),n}n=new WeakMap,i=new WeakMap,a=new WeakMap;var eo=Object.defineProperty,es=(e,t,r)=>t in e?eo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,el=(e,t,r)=>(es(e,"symbol"!=typeof t?t+"":t,r),r),eu=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(eu||{});let ec={0(e,t){let r=t.id,n=e.stack,i=e.stack.indexOf(r);if(-1!==i){let t=e.stack.slice();return t.splice(i,1),t.push(r),n=t,{...e,stack:n}}return{...e,stack:[...e.stack,r]}},1(e,t){let r=t.id,n=e.stack.indexOf(r);if(-1===n)return e;let i=e.stack.slice();return i.splice(n,1),{...e,stack:i}}};class ed extends et{constructor(){super(...arguments),el(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),el(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new ed({stack:[]})}reduce(e,t){return ea(t.type,ec,e,t)}}let ef=new B(()=>ed.new());var ep=r(6895);let em=function(e){let t=H(e);return c.useCallback((...e)=>t.current(...e),[t])};function eh(e,t,r=er){return(0,ep.useSyncExternalStoreWithSelector)(em(t=>e.subscribe(eg,t)),em(()=>e.state),em(()=>e.state),em(t),r)}function eg(e){return e}function ex(e,t){let r=(0,c.useId)(),n=ef.get(t),[i,a]=eh(n,(0,c.useCallback)(e=>[n.selectors.isTop(e,r),n.selectors.inStack(e,r)],[n,r]));return z(()=>{if(e)return n.actions.push(r),()=>n.actions.pop(r)},[n,e,r]),!!e&&(!a||i)}function ey(e){var t,r;return W.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(r=null==(t=e.current)?void 0:t.ownerDocument)?r:document:null:document}let ev=new Map,eb=new Map;function ej(e){var t;let r=null!=(t=eb.get(e))?t:0;return eb.set(e,r+1),0!==r||(ev.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let r=null!=(t=eb.get(e))?t:1;if(1===r?eb.delete(e):eb.set(e,r-1),1!==r)return;let n=ev.get(e);n&&(null===n["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",n["aria-hidden"]),e.inert=n.inert,ev.delete(e))})(e)}function eE(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function ew(e){return eE(e)&&"tagName"in e}function eN(e){return ew(e)&&"accessKey"in e}function eP(e){return ew(e)&&"tabIndex"in e}let eS=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),eA=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var eR=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(eR||{}),eC=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(eC||{}),ek=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(ek||{}),eO=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(eO||{}),e_=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(e_||{});function eT(e){null==e||e.focus({preventScroll:!0})}function eM(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:i=[]}={}){var a,o,s;let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?r?function(e,t=e=>e){return e.slice().sort((e,r)=>{let n=t(e),i=t(r);if(null===n||null===i)return 0;let a=n.compareDocumentPosition(i);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(eA)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(eS)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);i.length>0&&u.length>1&&(u=u.filter(e=>!i.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),n=null!=n?n:l.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(n))-1;if(4&t)return Math.max(0,u.indexOf(n))+1;if(8&t)return u.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,m=u.length,h;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(h=u[e])||h.focus(f),p+=c}while(h!==l.activeElement);return 6&t&&null!=(s=null==(o=null==(a=h)?void 0:a.matches)?void 0:o.call(a,"textarea,input"))&&s&&h.select(),2}function eI(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function eF(){return eI()||/Android/gi.test(window.navigator.userAgent)}function eD(...e){return(0,c.useMemo)(()=>ey(...e),[...e])}function eL(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var eU=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(eU||{}),e$=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(e$||{});function eq(){let e,t,r=(e=(0,c.useRef)([]),t=(0,c.useCallback)(t=>{for(let r of e.current)null!=r&&("function"==typeof r?r(t):r.current=t)},[]),(...r)=>{if(!r.every(e=>null==e))return e.current=r,t});return(0,c.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:r,defaultTag:n,features:i,visible:a=!0,name:o,mergeRefs:s}){s=null!=s?s:ez;let l=eH(t,e);if(a)return eW(l,r,n,o,s);let u=null!=i?i:0;if(2&u){let{static:e=!1,...t}=l;if(e)return eW(t,r,n,o,s)}if(1&u){let{unmount:e=!0,...t}=l;return ea(+!e,{0:()=>null,1:()=>eW({...t,hidden:!0,style:{display:"none"}},r,n,o,s)})}return eW(l,r,n,o,s)})({mergeRefs:r,...e}),[r])}function eW(e,t={},r,n,i){let{as:a=r,children:o,refName:s="ref",...l}=eV(e,["unmount","static"]),u=void 0!==e.ref?{[s]:e.ref}:{},d="function"==typeof o?o(t):o;"className"in l&&l.className&&"function"==typeof l.className&&(l.className=l.className(t)),l["aria-labelledby"]&&l["aria-labelledby"]===l.id&&(l["aria-labelledby"]=void 0);let f={};if(t){let e=!1,r=[];for(let[n,i]of Object.entries(t))"boolean"==typeof i&&(e=!0),!0===i&&r.push(n.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(f["data-headlessui-state"]=r.join(" "),r))f[`data-${e}`]=""}if(a===c.Fragment&&(Object.keys(eX(l)).length>0||Object.keys(eX(f)).length>0))if(!(0,c.isValidElement)(d)||Array.isArray(d)&&d.length>1){if(Object.keys(eX(l)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(eX(l)).concat(Object.keys(eX(f))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{var p;let e=d.props,t=null==e?void 0:e.className,r="function"==typeof t?(...e)=>eL(t(...e),l.className):eL(t,l.className),n=eH(d.props,eX(eV(l,["ref"])));for(let e in f)e in n&&delete f[e];return(0,c.cloneElement)(d,Object.assign({},n,f,u,{ref:i((p=d,c.version.split(".")[0]>="19"?p.props.ref:p.ref),u.ref)},r?{className:r}:{}))}return(0,c.createElement)(a,Object.assign({},eV(l,["ref"]),a!==c.Fragment&&u,a!==c.Fragment&&f),d)}function ez(...e){return e.every(e=>null==e)?void 0:t=>{for(let r of e)null!=r&&("function"==typeof r?r(t):r.current=t)}}function eH(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];if(t.disabled||t["aria-disabled"])for(let e in r)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(r[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in r)Object.assign(t,{[e](t,...n){for(let i of r[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;i(t,...n)}}});return t}function eB(e){var t;return Object.assign((0,c.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function eX(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function eV(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}var eG=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(eG||{});let eY=eB(function(e,t){var r;let{features:n=1,...i}=e,a={ref:t,"aria-hidden":(2&n)==2||(null!=(r=i["aria-hidden"])?r:void 0),hidden:(4&n)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&n)==4&&(2&n)!=2&&{display:"none"}}};return eq()({ourProps:a,theirProps:i,slot:{},defaultTag:"span",name:"Hidden"})}),eK=(0,c.createContext)(null);function eQ({children:e,node:t}){let[r,n]=(0,c.useState)(null),i=eZ(null!=t?t:r);return c.createElement(eK.Provider,{value:i},e,null===i&&c.createElement(eY,{features:eG.Hidden,ref:e=>{var t,r;if(e){for(let i of null!=(r=null==(t=ey(e))?void 0:t.querySelectorAll("html > *, body > *"))?r:[])if(i!==document.body&&i!==document.head&&ew(i)&&null!=i&&i.contains(e)){n(i);break}}}}))}function eZ(e=null){var t;return null!=(t=(0,c.useContext)(eK))?t:e}let eJ=function(e,t){let r=e(),n=new Set;return{getSnapshot:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...i){let a=t[e].call(r,...i);a&&(r=a,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var r;let n=null!=(r=this.get(e))?r:{doc:e,count:0,d:V(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n,i={doc:e,d:t,meta:function(e){let t={};for(let r of e)Object.assign(t,r(t));return t}(r)},a=[eI()?{before({doc:e,d:t,meta:r}){function n(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var r;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let r=V();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let i=null!=(r=window.scrollY)?r:window.pageYOffset,a=null;t.addEventListener(e,"click",t=>{if(eP(t.target))try{let r=t.target.closest("a");if(!r)return;let{hash:i}=new URL(r.href),o=e.querySelector(i);eP(o)&&!n(o)&&(a=o)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var r;if(eP(e.target)&&ew(r=e.target)&&"style"in r)if(n(e.target)){let r=e.target;for(;r.parentElement&&n(r.parentElement);)r=r.parentElement;t.style(r,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(eP(e.target)){var t;if(!(eN(t=e.target)&&"INPUT"===t.nodeName))if(n(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;i!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,i),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})})}}:{},{before({doc:e}){var t;let r=e.documentElement;n=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-r.clientWidth)},after({doc:e,d:t}){let r=e.documentElement,i=Math.max(0,r.clientWidth-r.offsetWidth),a=Math.max(0,n-i);t.style(r,"paddingRight",`${a}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];a.forEach(({before:e})=>null==e?void 0:e(i)),a.forEach(({after:e})=>null==e?void 0:e(i))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function e0(){let e,t=(e="undefined"==typeof document,"useSyncExternalStore"in d&&(0,d.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[r,n]=c.useState(W.isHandoffComplete);return r&&!1===W.isHandoffComplete&&n(!1),c.useEffect(()=>{!0!==r&&n(!0)},[r]),c.useEffect(()=>W.handoff(),[]),!t&&r}eJ.subscribe(()=>{let e=eJ.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let e="hidden"===t.get(r.doc),n=0!==r.count;(n&&!e||!n&&e)&&eJ.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),0===r.count&&eJ.dispatch("TEARDOWN",r)}});let e1=Symbol();function e2(...e){let t=(0,c.useRef)(e),r=em(e=>{for(let r of t.current)null!=r&&("function"==typeof r?r(e):r.current=e)});return e.every(e=>null==e||(null==e?void 0:e[e1]))?void 0:r}let e4=(0,c.createContext)(()=>{});function e3({value:e,children:t}){return c.createElement(e4.Provider,{value:e},t)}let e5=(0,c.createContext)(null);e5.displayName="OpenClosedContext";var e6=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(e6||{});function e7(){return(0,c.useContext)(e5)}function e9({value:e,children:t}){return c.createElement(e5.Provider,{value:e},t)}function e8({children:e}){return c.createElement(e5.Provider,{value:null},e)}let te=(0,c.createContext)(!1);function tt(e){return c.createElement(te.Provider,{value:e.force},e.children)}let tr=(0,c.createContext)(void 0),tn=(0,c.createContext)(null);tn.displayName="DescriptionContext";let ti=Object.assign(eB(function(e,t){let r=(0,c.useId)(),n=(0,c.useContext)(tr),{id:i=`headlessui-description-${r}`,...a}=e,o=function e(){let t=(0,c.useContext)(tn);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),s=e2(t);z(()=>o.register(i),[i,o.register]);let l=n||!1,u=(0,c.useMemo)(()=>({...o.slot,disabled:l}),[o.slot,l]),d={ref:s,...o.props,id:i};return eq()({ourProps:d,theirProps:a,slot:u,defaultTag:"p",name:o.name||"Description"})}),{});function ta(){let[e]=(0,c.useState)(V);return e}function to(){let e=(0,c.useRef)(!1);return z(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function ts(e){em(e),(0,c.useRef)(!1)}var tl=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(tl||{});function tu(e,t){(0,c.useRef)([]),em(e)}let tc=[];function td(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let r of e.current)ew(r.current)&&t.add(r.current);return t}var tf=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(tf||{});let tp=Object.assign(eB(function(e,t){let r,n=(0,c.useRef)(null),i=e2(n,t),{initialFocus:a,initialFocusFallback:o,containers:s,features:l=15,...u}=e;e0()||(l=0);let d=eD(n);!function(e,{ownerDocument:t}){let r=!!(8&e),n=function(e=!0){let t=(0,c.useRef)(tc.slice());return tu(([e],[r])=>{!0===r&&!1===e&&X(()=>{t.current.splice(0)}),!1===r&&!0===e&&(t.current=tc.slice())},[e,tc,t]),em(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);tu(()=>{r||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&eT(n())},[r]),ts(()=>{r&&eT(n())})}(l,{ownerDocument:d});let f=function(e,{ownerDocument:t,container:r,initialFocus:n,initialFocusFallback:i}){let a=(0,c.useRef)(null),o=ex(!!(1&e),"focus-trap#initial-focus"),s=to();return tu(()=>{if(0===e)return;if(!o){null!=i&&i.current&&eT(i.current);return}let l=r.current;l&&X(()=>{if(!s.current)return;let r=null==t?void 0:t.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===r){a.current=r;return}}else if(l.contains(r)){a.current=r;return}if(null!=n&&n.current)eT(n.current);else{if(16&e){if(eM(l,eR.First|eR.AutoFocus)!==eC.Error)return}else if(eM(l,eR.First)!==eC.Error)return;if(null!=i&&i.current&&(eT(i.current),(null==t?void 0:t.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=null==t?void 0:t.activeElement})},[i,o,e]),a}(l,{ownerDocument:d,container:n,initialFocus:a,initialFocusFallback:o});!function(e,{ownerDocument:t,container:r,containers:n,previousActiveElement:i}){var a;let o=to(),s=!!(4&e);null==t||t.defaultView,H(e=>{if(!s||!o.current)return;let t=td(n);eN(r.current)&&t.add(r.current);let a=i.current;if(!a)return;let l=e.target;eN(l)?tm(t,l)?(i.current=l,eT(l)):(e.preventDefault(),e.stopPropagation(),eT(a)):eT(i.current)})}(l,{ownerDocument:d,container:n,containers:s,previousActiveElement:f});let p=(r=(0,c.useRef)(0),H(e=>{"Tab"===e.key&&(r.current=+!!e.shiftKey)}),r),m=em(e=>{if(!eN(n.current))return;let t=n.current;ea(p.current,{[tl.Forwards]:()=>{eM(t,eR.First,{skipElements:[e.relatedTarget,o]})},[tl.Backwards]:()=>{eM(t,eR.Last,{skipElements:[e.relatedTarget,o]})}})}),h=ex(!!(2&l),"focus-trap#tab-lock"),g=ta(),x=(0,c.useRef)(!1),y=eq();return c.createElement(c.Fragment,null,h&&c.createElement(eY,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:eG.Focusable}),y({ourProps:{ref:i,onKeyDown(e){"Tab"==e.key&&(x.current=!0,g.requestAnimationFrame(()=>{x.current=!1}))},onBlur(e){if(!(4&l))return;let t=td(s);eN(n.current)&&t.add(n.current);let r=e.relatedTarget;eP(r)&&"true"!==r.dataset.headlessuiFocusGuard&&(tm(t,r)||(x.current?eM(n.current,ea(p.current,{[tl.Forwards]:()=>eR.Next,[tl.Backwards]:()=>eR.Previous})|eR.WrapAround,{relativeTo:e.target}):eP(e.target)&&eT(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),h&&c.createElement(eY,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:eG.Focusable}))}),{features:tf});function tm(e,t){for(let r of e)if(r.contains(t))return!0;return!1}var th=r(1215);let tg=c.Fragment,tx=eB(function(e,t){let{ownerDocument:r=null,...n}=e,i=(0,c.useRef)(null),a=e2(function(e,t=!0){return Object.assign(e,{[e1]:t})}(e=>{i.current=e}),t),o=eD(i),s=null!=r?r:o,l=function(e){let t=(0,c.useContext)(te),r=(0,c.useContext)(tv),[n,i]=(0,c.useState)(()=>{var n;if(!t&&null!==r)return null!=(n=r.current)?n:null;if(W.isServer)return null;let i=null==e?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(null===e)return null;let a=e.createElement("div");return a.setAttribute("id","headlessui-portal-root"),e.body.appendChild(a)});return n}(s),[u]=(0,c.useState)(()=>{var e;return W.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null}),d=(0,c.useContext)(tb),f=e0();z(()=>{!l||!u||l.contains(u)||(u.setAttribute("data-headlessui-portal",""),l.appendChild(u))},[l,u]),z(()=>{if(u&&d)return d.register(u)},[d,u]),ts(()=>{var e;l&&u&&(eE(u)&&l.contains(u)&&l.removeChild(u),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))});let p=eq();return f&&l&&u?(0,th.createPortal)(p({ourProps:{ref:a},theirProps:n,slot:{},defaultTag:tg,name:"Portal"}),u):null}),ty=c.Fragment,tv=(0,c.createContext)(null),tb=(0,c.createContext)(null),tj=eB(function(e,t){let r=e2(t),{enabled:n=!0,ownerDocument:i,...a}=e,o=eq();return n?c.createElement(tx,{...a,ownerDocument:i,ref:r}):o({ourProps:{ref:r},theirProps:a,slot:{},defaultTag:tg,name:"Portal"})}),tE=eB(function(e,t){let{target:r,...n}=e,i={ref:e2(t)},a=eq();return c.createElement(tv.Provider,{value:r},a({ourProps:i,theirProps:n,defaultTag:ty,name:"Popover.Group"}))}),tw=Object.assign(tj,{Group:tE});"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(o=null==process?void 0:process.env)?void 0:o.NODE_ENV)==="test"&&void 0===(null==(s=null==Element?void 0:Element.prototype)?void 0:s.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var tN=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(tN||{});function tP(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:tO)!==c.Fragment||1===c.Children.count(e.children)}let tS=(0,c.createContext)(null);tS.displayName="TransitionContext";var tA=(e=>(e.Visible="visible",e.Hidden="hidden",e))(tA||{});let tR=(0,c.createContext)(null);function tC(e){return"children"in e?tC(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function tk(e,t){let r=H(e),n=(0,c.useRef)([]),i=to(),a=ta(),o=em((e,t=e$.Hidden)=>{let o=n.current.findIndex(({el:t})=>t===e);-1!==o&&(ea(t,{[e$.Unmount](){n.current.splice(o,1)},[e$.Hidden](){n.current[o].state="hidden"}}),a.microTask(()=>{var e;!tC(n)&&i.current&&(null==(e=r.current)||e.call(r))}))}),s=em(e=>{let t=n.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):n.current.push({el:e,state:"visible"}),()=>o(e,e$.Unmount)}),l=(0,c.useRef)([]),u=(0,c.useRef)(Promise.resolve()),d=(0,c.useRef)({enter:[],leave:[]}),f=em((e,r,n)=>{l.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(([t])=>t!==e)),null==t||t.chains.current[r].push([e,new Promise(e=>{l.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(d.current[r].map(([e,t])=>t)).then(()=>e())})]),"enter"===r?u.current=u.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),p=em((e,t,r)=>{Promise.all(d.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=l.current.shift())||e()}).then(()=>r(t))});return(0,c.useMemo)(()=>({children:n,register:s,unregister:o,onStart:f,onStop:p,wait:u,chains:d}),[s,o,n,f,p,d,u])}tR.displayName="NestingContext";let tO=c.Fragment,t_=eU.RenderStrategy,tT=eB(function(e,t){let{show:r,appear:n=!1,unmount:i=!0,...a}=e,o=(0,c.useRef)(null),s=e2(...tP(e)?[o,t]:null===t?[]:[t]);e0();let l=e7();if(void 0===r&&null!==l&&(r=(l&e6.Open)===e6.Open),void 0===r)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,d]=(0,c.useState)(r?"visible":"hidden"),f=tk(()=>{r||d("hidden")}),[p,m]=(0,c.useState)(!0),h=(0,c.useRef)([r]);z(()=>{!1!==p&&h.current[h.current.length-1]!==r&&(h.current.push(r),m(!1))},[h,r]);let g=(0,c.useMemo)(()=>({show:r,appear:n,initial:p}),[r,n,p]);z(()=>{r?d("visible"):tC(f)||null===o.current||d("hidden")},[r,f]);let x={unmount:i},y=em(()=>{var t;p&&m(!1),null==(t=e.beforeEnter)||t.call(e)}),v=em(()=>{var t;p&&m(!1),null==(t=e.beforeLeave)||t.call(e)}),b=eq();return c.createElement(tR.Provider,{value:f},c.createElement(tS.Provider,{value:g},b({ourProps:{...x,as:c.Fragment,children:c.createElement(tM,{ref:s,...x,...a,beforeEnter:y,beforeLeave:v})},theirProps:{},defaultTag:c.Fragment,features:t_,visible:"visible"===u,name:"Transition"})))}),tM=eB(function(e,t){var r,n;let{transition:i=!0,beforeEnter:a,afterEnter:o,beforeLeave:s,afterLeave:l,enter:u,enterFrom:d,enterTo:f,entered:p,leave:m,leaveFrom:h,leaveTo:g,...x}=e,[y,v]=(0,c.useState)(null),b=(0,c.useRef)(null),j=tP(e),E=e2(...j?[b,t,v]:null===t?[]:[t]),w=null==(r=x.unmount)||r?e$.Unmount:e$.Hidden,{show:N,appear:P,initial:S}=function(){let e=(0,c.useContext)(tS);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[A,R]=(0,c.useState)(N?"visible":"hidden"),C=function(){let e=(0,c.useContext)(tR);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:k,unregister:O}=C;z(()=>k(b),[k,b]),z(()=>{if(w===e$.Hidden&&b.current)return N&&"visible"!==A?void R("visible"):ea(A,{hidden:()=>O(b),visible:()=>k(b)})},[A,b,k,O,N,w]);let _=e0();z(()=>{if(j&&_&&"visible"===A&&null===b.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[b,A,_,j]);let T=P&&N&&S,M=(0,c.useRef)(!1),I=tk(()=>{M.current||(R("hidden"),O(b))},C),[,F]=function(e,t,r,n){let[i,a]=(0,c.useState)(r),{hasFlag:o,addFlag:s,removeFlag:l}=function(e=0){let[t,r]=(0,c.useState)(e),n=(0,c.useCallback)(e=>r(e),[t]),i=(0,c.useCallback)(e=>r(t=>t|e),[t]),a=(0,c.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:n,addFlag:i,hasFlag:a,removeFlag:(0,c.useCallback)(e=>r(t=>t&~e),[r]),toggleFlag:(0,c.useCallback)(e=>r(t=>t^e),[r])}}(e&&i?3:0),u=(0,c.useRef)(!1),d=(0,c.useRef)(!1);return z(()=>{var i;if(e){if(r&&a(!0),!t){r&&s(3);return}return null==(i=null==n?void 0:n.start)||i.call(n,r),function(e,{prepare:t,run:r,done:n,inFlight:i}){let a=V();return function(e,{inFlight:t,prepare:r}){if(null!=t&&t.current)return r();let n=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=n}(e,{prepare:t,inFlight:i}),a.nextFrame(()=>{r(),a.requestAnimationFrame(()=>{a.add(function(e,t){var r,n;let i=V();if(!e)return i.dispose;let a=!1;i.add(()=>{a=!0});let o=null!=(n=null==(r=e.getAnimations)?void 0:r.call(e).filter(e=>e instanceof CSSTransition))?n:[];return 0===o.length?t():Promise.allSettled(o.map(e=>e.finished)).then(()=>{a||t()}),i.dispose}(e,n))})}),a.dispose}(t,{inFlight:u,prepare(){d.current?d.current=!1:d.current=u.current,u.current=!0,d.current||(r?(s(3),l(4)):(s(4),l(2)))},run(){d.current?r?(l(3),s(4)):(l(4),s(3)):r?l(1):s(1)},done(){var e;d.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(u.current=!1,l(7),r||a(!1),null==(e=null==n?void 0:n.end)||e.call(n,r))}})}},[e,r,t,ta()]),e?[i,{closed:o(1),enter:o(2),leave:o(4),transition:o(2)||o(4)}]:[r,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!i||!j||!_||S&&!P),y,N,{start:em(e=>{M.current=!0,I.onStart(b,e?"enter":"leave",e=>{"enter"===e?null==a||a():"leave"===e&&(null==s||s())})}),end:em(e=>{let t=e?"enter":"leave";M.current=!1,I.onStop(b,t,e=>{"enter"===e?null==o||o():"leave"===e&&(null==l||l())}),"leave"!==t||tC(I)||(R("hidden"),O(b))})}),D=eX({ref:E,className:(null==(n=eL(x.className,T&&u,T&&d,F.enter&&u,F.enter&&F.closed&&d,F.enter&&!F.closed&&f,F.leave&&m,F.leave&&!F.closed&&h,F.leave&&F.closed&&g,!F.transition&&N&&p))?void 0:n.trim())||void 0,...function(e){let t={};for(let r in e)!0===e[r]&&(t[`data-${r}`]="");return t}(F)}),L=0;"visible"===A&&(L|=e6.Open),"hidden"===A&&(L|=e6.Closed),N&&"hidden"===A&&(L|=e6.Opening),N||"visible"!==A||(L|=e6.Closing);let U=eq();return c.createElement(tR.Provider,{value:I},c.createElement(e9,{value:L},U({ourProps:D,theirProps:x,defaultTag:tO,features:t_,visible:"visible"===A,name:"Transition.Child"})))}),tI=eB(function(e,t){let r=null!==(0,c.useContext)(tS),n=null!==e7();return c.createElement(c.Fragment,null,!r&&n?c.createElement(tT,{ref:t,...e}):c.createElement(tM,{ref:t,...e}))}),tF=Object.assign(tT,{Child:tI,Root:tT});var tD=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(tD||{}),tL=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(tL||{});let tU={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},t$=(0,c.createContext)(null);function tq(e){let t=(0,c.useContext)(t$);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,tq),t}return t}function tW(e,t){return ea(t.type,tU,e,t)}t$.displayName="DialogContext";let tz=eB(function(e,t){let r,n,i,a,o,s,l,u,d,f=(0,c.useId)(),{id:p=`headlessui-dialog-${f}`,open:m,onClose:h,initialFocus:g,role:x="dialog",autoFocus:y=!0,__demoMode:v=!1,unmount:b=!1,...j}=e,E=(0,c.useRef)(!1);x="dialog"===x||"alertdialog"===x?x:(E.current||(E.current=!0,console.warn(`Invalid role [${x}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let w=e7();void 0===m&&null!==w&&(m=(w&e6.Open)===e6.Open);let N=(0,c.useRef)(null),P=e2(N,t),S=eD(N),A=+!m,[R,C]=(0,c.useReducer)(tW,{titleId:null,descriptionId:null,panelRef:(0,c.createRef)()}),k=em(()=>h(!1)),O=em(e=>C({type:0,id:e})),_=!!e0()&&0===A,[T,M]=(r=(0,c.useContext)(tb),n=(0,c.useRef)([]),i=em(e=>(n.current.push(e),r&&r.register(e),()=>a(e))),a=em(e=>{let t=n.current.indexOf(e);-1!==t&&n.current.splice(t,1),r&&r.unregister(e)}),o=(0,c.useMemo)(()=>({register:i,unregister:a,portals:n}),[i,a,n]),[n,(0,c.useMemo)(()=>function({children:e}){return c.createElement(tb.Provider,{value:o},e)},[o])]),I=eZ(),{resolveContainers:F}=function({defaultContainers:e=[],portals:t,mainTreeNode:r}={}){let n=eD(r),i=em(()=>{var i,a;let o=[];for(let t of e)null!==t&&(ew(t)?o.push(t):"current"in t&&ew(t.current)&&o.push(t.current));if(null!=t&&t.current)for(let e of t.current)o.push(e);for(let e of null!=(i=null==n?void 0:n.querySelectorAll("html > *, body > *"))?i:[])e!==document.body&&e!==document.head&&ew(e)&&"headlessui-portal-root"!==e.id&&(r&&(e.contains(r)||e.contains(null==(a=null==r?void 0:r.getRootNode())?void 0:a.host))||o.some(t=>e.contains(t))||o.push(e));return o});return{resolveContainers:i,contains:em(e=>i().some(t=>t.contains(e)))}}({mainTreeNode:I,portals:T,defaultContainers:[{get current(){var L;return null!=(L=R.panelRef.current)?L:N.current}}]}),U=null!==w&&(w&e6.Closing)===e6.Closing;!function(e,{allowed:t,disallowed:r}={}){let n=ex(e,"inert-others");z(()=>{var e,i;if(!n)return;let a=V();for(let t of null!=(e=null==r?void 0:r())?e:[])t&&a.add(ej(t));let o=null!=(i=null==t?void 0:t())?i:[];for(let e of o){if(!e)continue;let t=ey(e);if(!t)continue;let r=e.parentElement;for(;r&&r!==t.body;){for(let e of r.children)o.some(t=>e.contains(t))||a.add(ej(e));r=r.parentElement}}return a.dispose},[n,t,r])}(!v&&!U&&_,{allowed:em(()=>{var e,t;return[null!=(t=null==(e=N.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:em(()=>{var e;return[null!=(e=null==I?void 0:I.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let $=ef.get(null);z(()=>{if(_)return $.actions.push(p),()=>$.actions.pop(p)},[$,p,_]);let q=eh($,(0,c.useCallback)(e=>$.selectors.isTop(e,p),[$,p]));s=H(e=>{e.preventDefault(),k()}),l=(0,c.useCallback)(function(e,t){if(e.defaultPrevented)return;let r=t(e);if(null!==r&&r.getRootNode().contains(r)&&r.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(F))if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return function(e,t=0){var r;return e!==(null==(r=ey(e))?void 0:r.body)&&ea(t,{0:()=>e.matches(eS),1(){let t=e;for(;null!==t;){if(t.matches(eS))return!0;t=t.parentElement}return!1}})}(r,eO.Loose)||-1===r.tabIndex||e.preventDefault(),s.current(e,r)}},[s,F]),u=(0,c.useRef)(null),H(e=>{var t,r;eF()||(u.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)}),H(e=>{if(eF()||!u.current)return;let t=u.current;return u.current=null,l(e,()=>t)}),d=(0,c.useRef)({x:0,y:0}),H(e=>{d.current.x=e.touches[0].clientX,d.current.y=e.touches[0].clientY}),H(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-d.current.x)>=30||Math.abs(t.y-d.current.y)>=30))return l(e,()=>eP(e.target)?e.target:null)}),H(e=>l(e,()=>{var e;return eN(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null})),function(e,t="undefined"!=typeof document?document.defaultView:null,r){let n=ex(e,"escape");H(e=>{n&&(e.defaultPrevented||e.key===D.Escape&&r(e))})}(q,null==S?void 0:S.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),k()}),function(e,t,r=()=>[document.body]){!function(e,t,r=()=>({containers:[]})){let n=(0,c.useSyncExternalStore)(eJ.subscribe,eJ.getSnapshot,eJ.getSnapshot),i=t?n.get(t):void 0;i&&i.count,z(()=>{if(!(!t||!e))return eJ.dispatch("PUSH",t,r),()=>eJ.dispatch("POP",t,r)},[e,t])}(ex(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}})}(!v&&!U&&_,S,F),H(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&k()});let[W,B]=function(){let[e,t]=(0,c.useState)([]);return[e.length>0?e.join(" "):void 0,(0,c.useMemo)(()=>function(e){let r=em(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),n=(0,c.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props,value:e.value}),[r,e.slot,e.name,e.props,e.value]);return c.createElement(tn.Provider,{value:n},e.children)},[t])]}(),X=(0,c.useMemo)(()=>[{dialogState:A,close:k,setTitleId:O,unmount:b},R],[A,R,k,O,b]),G=(0,c.useMemo)(()=>({open:0===A}),[A]),Y={ref:P,id:p,role:x,tabIndex:-1,"aria-modal":v?void 0:0===A||void 0,"aria-labelledby":R.titleId,"aria-describedby":W,unmount:b},K=!function(){var e;let[t]=(0,c.useState)(()=>null),[r,n]=(0,c.useState)(null!=(e=null==t?void 0:t.matches)&&e);return z(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){n(e.matches)}},[t]),r}(),Q=tf.None;_&&!v&&(Q|=tf.RestoreFocus,Q|=tf.TabLock,y&&(Q|=tf.AutoFocus),K&&(Q|=tf.InitialFocus));let Z=eq();return c.createElement(e8,null,c.createElement(tt,{force:!0},c.createElement(tw,null,c.createElement(t$.Provider,{value:X},c.createElement(tE,{target:N},c.createElement(tt,{force:!1},c.createElement(B,{slot:G},c.createElement(M,null,c.createElement(tp,{initialFocus:g,initialFocusFallback:N,containers:F,features:Q},c.createElement(e3,{value:k},Z({ourProps:Y,theirProps:j,slot:G,defaultTag:tH,features:tB,visible:0===A,name:"Dialog"})))))))))))}),tH="div",tB=eU.RenderStrategy|eU.Static,tX=Object.assign(eB(function(e,t){let{transition:r=!1,open:n,...i}=e,a=e7(),o=e.hasOwnProperty("open")||null!==a,s=e.hasOwnProperty("onClose");if(!o&&!s)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!o)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!s)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!a&&"boolean"!=typeof e.open)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(void 0!==n||r)&&!i.static?c.createElement(eQ,null,c.createElement(tF,{show:n,transition:r,unmount:i.unmount},c.createElement(tz,{ref:t,...i}))):c.createElement(eQ,null,c.createElement(tz,{ref:t,open:n,...i}))}),{Panel:eB(function(e,t){let r=(0,c.useId)(),{id:n=`headlessui-dialog-panel-${r}`,transition:i=!1,...a}=e,[{dialogState:o,unmount:s},l]=tq("Dialog.Panel"),u=e2(t,l.panelRef),d=(0,c.useMemo)(()=>({open:0===o}),[o]),f=em(e=>{e.stopPropagation()}),p=i?tI:c.Fragment,m=eq();return c.createElement(p,{...i?{unmount:s}:{}},m({ourProps:{ref:u,id:n,onClick:f},theirProps:a,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:(eB(function(e,t){let{transition:r=!1,...n}=e,[{dialogState:i,unmount:a}]=tq("Dialog.Backdrop"),o=(0,c.useMemo)(()=>({open:0===i}),[i]),s=r?tI:c.Fragment,l=eq();return c.createElement(s,{...r?{unmount:a}:{}},l({ourProps:{ref:t,"aria-hidden":!0},theirProps:n,slot:o,defaultTag:"div",name:"Dialog.Backdrop"}))}),eB(function(e,t){let r=(0,c.useId)(),{id:n=`headlessui-dialog-title-${r}`,...i}=e,[{dialogState:a,setTitleId:o}]=tq("Dialog.Title"),s=e2(t),l=(0,c.useMemo)(()=>({open:0===a}),[a]);return eq()({ourProps:{ref:s,id:n},theirProps:i,slot:l,defaultTag:"h2",name:"Dialog.Title"})})),Description:ti}),tV=v("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),tG=v("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),tY=v("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);function tK({isOpen:e,onClose:t,initialMode:r="signin"}){let[n,i]=(0,c.useState)(r),[a,o]=(0,c.useState)(""),[s,l]=(0,c.useState)(""),[d,f]=(0,c.useState)(!1),[p,m]=(0,c.useState)(""),{signIn:h,signUp:g}=(0,w.A)(),x=async e=>{e.preventDefault(),f(!0),m("");try{let{error:e}="signin"===n?await h(a,s):await g(a,s);e?m(e.message):"signup"===n?m("Check your email for the confirmation link!"):t()}catch(e){m("An unexpected error occurred")}finally{f(!1)}},y=()=>{o(""),l(""),m("")};return(0,u.jsxs)(tX,{open:e,onClose:t,className:"relative z-50",children:[(0,u.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,u.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,u.jsxs)(tX.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,u.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,u.jsx)(tX.Title,{className:"text-xl font-semibold",children:"signin"===n?"Sign In":"Create Account"}),(0,u.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,u.jsx)(tV,{className:"w-5 h-5"})})]}),(0,u.jsxs)("form",{onSubmit:x,className:"p-6 space-y-4",children:[p&&(0,u.jsx)("div",{className:`p-3 rounded-lg text-sm ${p.includes("Check your email")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:p}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(tG,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,u.jsx)("input",{id:"email",type:"email",value:a,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(tY,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,u.jsx)("input",{id:"password",type:"password",value:s,onChange:e=>l(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0,minLength:6})]})]}),(0,u.jsx)("button",{type:"submit",disabled:d,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?"Loading...":"signin"===n?"Sign In":"Create Account"}),(0,u.jsx)("div",{className:"text-center",children:(0,u.jsx)("button",{type:"button",onClick:()=>{i("signin"===n?"signup":"signin"),y()},className:"text-sm text-blue-600 hover:text-blue-700 transition-colors",children:"signin"===n?"Don't have an account? Sign up":"Already have an account? Sign in"})})]})]})})]})}let tQ=v("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var tZ="basil",tJ="https://js.stripe.com",t0="".concat(tJ,"/").concat(tZ,"/stripe.js"),t1=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,t2=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,t4=function(){for(var e=document.querySelectorAll('script[src^="'.concat(tJ,'"]')),t=0;t<e.length;t++){var r,n=e[t];if(r=n.src,t1.test(r)||t2.test(r))return n}return null},t3=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(t0).concat(t);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(r),r},t5=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:t})},t6=null,t7=null,t9=null,t8=function(e,t,r){if(null===e)return null;var n,i=t[0].match(/^pk_test/),a=3===(n=e.version)?"v3":n;i&&a!==tZ&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(tZ,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var o=e.apply(void 0,t);return t5(o,r),o},re=!1,rt=function(){return l?l:l=(null!==t6?t6:(t6=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var r,n=t4();n?n&&null!==t9&&null!==t7&&(n.removeEventListener("load",t9),n.removeEventListener("error",t7),null==(r=n.parentNode)||r.removeChild(n),n=t3(null)):n=t3(null),t9=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},t7=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",t9),n.addEventListener("error",t7)}catch(e){t(e);return}})).catch(function(e){return t6=null,Promise.reject(e)})).catch(function(e){return l=null,Promise.reject(e)})};Promise.resolve().then(function(){return rt()}).catch(function(e){re||console.warn(e)});var rr=r(7955);function rn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ri(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rn(Object(r),!0).forEach(function(t){ro(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rn(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ra(e){return(ra="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ro(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rs(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function rl(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r,n,i=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=i){var a=[],o=!0,s=!1;try{for(i=i.call(e);!(o=(r=i.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){s=!0,n=e}finally{try{o||null==i.return||i.return()}finally{if(s)throw n}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ru(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ru(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ru(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var rc=function(e,t,r){var n=!!r,i=c.useRef(r);c.useEffect(function(){i.current=r},[r]),c.useEffect(function(){if(!n||!e)return function(){};var r=function(){i.current&&i.current.apply(i,arguments)};return e.on(t,r),function(){e.off(t,r)}},[n,t,e,i])},rd=function(e){var t=c.useRef(e);return c.useEffect(function(){t.current=e},[e]),t.current},rf=function(e){return null!==e&&"object"===ra(e)},rp="[object Object]",rm=function e(t,r){if(!rf(t)||!rf(r))return t===r;var n=Array.isArray(t);if(n!==Array.isArray(r))return!1;var i=Object.prototype.toString.call(t)===rp;if(i!==(Object.prototype.toString.call(r)===rp))return!1;if(!i&&!n)return t===r;var a=Object.keys(t),o=Object.keys(r);if(a.length!==o.length)return!1;for(var s={},l=0;l<a.length;l+=1)s[a[l]]=!0;for(var u=0;u<o.length;u+=1)s[o[u]]=!0;var c=Object.keys(s);return c.length===a.length&&c.every(function(n){return e(t[n],r[n])})},rh=function(e,t,r){return rf(e)?Object.keys(e).reduce(function(n,i){var a=!rf(t)||!rm(e[i],t[i]);return r.includes(i)?(a&&console.warn("Unsupported prop change: options.".concat(i," is not a mutable property.")),n):a?ri(ri({},n||{}),{},ro({},i,e[i])):n},null):null},rg="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",rx=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rg;if(null===e||rf(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},ry=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rg;if(rf(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return rx(e,t)})};var r=rx(e,t);return null===r?{tag:"empty"}:{tag:"sync",stripe:r}},rv=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},rb=c.createContext(null);rb.displayName="ElementsContext";var rj=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},rE=function(e){var t=e.stripe,r=e.options,n=e.children,i=c.useMemo(function(){return ry(t)},[t]),a=rl(c.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),o=a[0],s=a[1];c.useEffect(function(){var e=!0,t=function(e){s(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||o.stripe?"sync"!==i.tag||o.stripe||t(i.stripe):i.stripePromise.then(function(r){r&&e&&t(r)}),function(){e=!1}},[i,o,r]);var l=rd(t);c.useEffect(function(){null!==l&&l!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[l,t]);var u=rd(r);return c.useEffect(function(){if(o.elements){var e=rh(r,u,["clientSecret","fonts"]);e&&o.elements.update(e)}},[r,u,o.elements]),c.useEffect(function(){rv(o.stripe)},[o.stripe]),c.createElement(rb.Provider,{value:o},n)};rE.propTypes={stripe:rr.any,options:rr.object};rr.func.isRequired;var rw=["on","session"],rN=c.createContext(null);rN.displayName="CheckoutSdkContext";var rP=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e};c.createContext(null).displayName="CheckoutContext";rr.any,rr.shape({fetchClientSecret:rr.func.isRequired,elementsOptions:rr.object}).isRequired;var rS=function(e){var t=c.useContext(rN),r=c.useContext(rb);if(t&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return t?rP(t,e):rj(r,e)},rA=["mode"],rR=function(e,t){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),n=t?function(e){rS("mounts <".concat(r,">"));var t=e.id,n=e.className;return c.createElement("div",{id:t,className:n})}:function(t){var n,i=t.id,a=t.className,o=t.options,s=void 0===o?{}:o,l=t.onBlur,u=t.onFocus,d=t.onReady,f=t.onChange,p=t.onEscape,m=t.onClick,h=t.onLoadError,g=t.onLoaderStart,x=t.onNetworksChange,y=t.onConfirm,v=t.onCancel,b=t.onShippingAddressChange,j=t.onShippingRateChange,E=rS("mounts <".concat(r,">")),w="elements"in E?E.elements:null,N="checkoutSdk"in E?E.checkoutSdk:null,P=rl(c.useState(null),2),S=P[0],A=P[1],R=c.useRef(null),C=c.useRef(null);rc(S,"blur",l),rc(S,"focus",u),rc(S,"escape",p),rc(S,"click",m),rc(S,"loaderror",h),rc(S,"loaderstart",g),rc(S,"networkschange",x),rc(S,"confirm",y),rc(S,"cancel",v),rc(S,"shippingaddresschange",b),rc(S,"shippingratechange",j),rc(S,"change",f),d&&(n="expressCheckout"===e?d:function(){d(S)}),rc(S,"ready",n),c.useLayoutEffect(function(){if(null===R.current&&null!==C.current&&(w||N)){var t=null;if(N)switch(e){case"payment":t=N.createPaymentElement(s);break;case"address":if("mode"in s){var n=s.mode,i=rs(s,rA);if("shipping"===n)t=N.createShippingAddressElement(i);else if("billing"===n)t=N.createBillingAddressElement(i);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=N.createExpressCheckoutElement(s);break;case"currencySelector":t=N.createCurrencySelectorElement();break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else w&&(t=w.create(e,s));R.current=t,A(t),t&&t.mount(C.current)}},[w,N,s]);var k=rd(s);return c.useEffect(function(){if(R.current){var e=rh(s,k,["paymentRequest"]);e&&"update"in R.current&&R.current.update(e)}},[s,k]),c.useLayoutEffect(function(){return function(){if(R.current&&"function"==typeof R.current.destroy)try{R.current.destroy(),R.current=null}catch(e){}}},[]),c.createElement("div",{id:i,className:a,ref:C})};return n.propTypes={id:rr.string,className:rr.string,onChange:rr.func,onBlur:rr.func,onFocus:rr.func,onReady:rr.func,onEscape:rr.func,onClick:rr.func,onLoadError:rr.func,onLoaderStart:rr.func,onNetworksChange:rr.func,onConfirm:rr.func,onCancel:rr.func,onShippingAddressChange:rr.func,onShippingRateChange:rr.func,options:rr.object},n.displayName=r,n.__elementType=e,n},rC="undefined"==typeof window,rk=c.createContext(null);rk.displayName="EmbeddedCheckoutProviderContext";rR("auBankAccount",rC);var rO=rR("card",rC);rR("cardNumber",rC),rR("cardExpiry",rC),rR("cardCvc",rC),rR("fpxBank",rC),rR("iban",rC),rR("idealBank",rC),rR("p24Bank",rC),rR("epsBank",rC),rR("payment",rC),rR("expressCheckout",rC),rR("currencySelector",rC),rR("paymentRequestButton",rC),rR("linkAuthentication",rC),rR("address",rC),rR("shippingAddress",rC),rR("paymentMethodMessaging",rC),rR("affirmMessage",rC),rR("afterpayClearpayMessage",rC);let r_=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];re=!0;var n=Date.now();return rt().then(function(e){return t8(e,t,n)})}("your_stripe_publishable_key_here");function rT({onSuccess:e,onError:t}){var r;let n=rS("calls useStripe()").stripe,i=(r="calls useElements()",rj(c.useContext(rb),r)).elements,[a,o]=(0,c.useState)(!1),{refreshProfile:s}=(0,w.A)(),l=async r=>{if(r.preventDefault(),n&&i){o(!0);try{let r=await fetch("/api/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({packageType:"basic"})}),{clientSecret:a,error:o}=await r.json();if(o)return void t(o);let{error:l}=await n.confirmCardPayment(a,{payment_method:{card:i.getElement(rO)}});l?t(l.message||"Payment failed"):(await s(),e())}catch(e){t("Payment processing failed")}finally{o(!1)}}};return(0,u.jsxs)("form",{onSubmit:l,className:"space-y-4",children:[(0,u.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{className:"font-semibold text-blue-900",children:"5 Credits Package"}),(0,u.jsx)("p",{className:"text-sm text-blue-700",children:"Perfect for occasional use"})]}),(0,u.jsxs)("div",{className:"text-right",children:[(0,u.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:"$5.00"}),(0,u.jsx)("div",{className:"text-sm text-blue-700",children:"$1.00 per credit"})]})]})}),(0,u.jsx)("div",{className:"border border-gray-300 rounded-lg p-3",children:(0,u.jsx)(rO,{options:{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}}}}})}),(0,u.jsxs)("button",{type:"submit",disabled:!n||a,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:[(0,u.jsx)(M,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:a?"Processing...":"Buy 5 Credits for $5.00"})]})]})}function rM({isOpen:e,onClose:t}){let[r,n]=(0,c.useState)(!1),[i,a]=(0,c.useState)(""),o=()=>{n(!1),a(""),t()};return(0,u.jsxs)(tX,{open:e,onClose:o,className:"relative z-50",children:[(0,u.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,u.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,u.jsxs)(tX.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,u.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,u.jsx)(tX.Title,{className:"text-xl font-semibold",children:"Buy Credits"}),(0,u.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,u.jsx)(tV,{className:"w-5 h-5"})})]}),(0,u.jsx)("div",{className:"p-6",children:r?(0,u.jsxs)("div",{className:"text-center py-8",children:[(0,u.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,u.jsx)(tQ,{className:"w-8 h-8 text-green-600"})}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Payment Successful!"}),(0,u.jsx)("p",{className:"text-gray-600",children:"5 credits have been added to your account."})]}):(0,u.jsxs)(u.Fragment,{children:[i&&(0,u.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-lg text-sm",children:i}),(0,u.jsx)(rE,{stripe:r_,children:(0,u.jsx)(rT,{onSuccess:()=>{n(!0),a(""),setTimeout(()=>{n(!1),t()},2e3)},onError:e=>{a(e),n(!1)}})}),(0,u.jsx)("div",{className:"mt-4 text-xs text-gray-500 text-center",children:"Your payment is secured by Stripe. We don't store your card details."})]})})]})})]})}function rI(){let[e,t]=(0,c.useState)("upload"),[r,n]=(0,c.useState)(!1),[i,a]=(0,c.useState)(!1),[o,s]=(0,c.useState)("signin"),{user:l,loading:d}=(0,w.A)(),f=()=>{s("signup"),n(!0)};return d?(0,u.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,u.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"})}):(0,u.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,u.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,u.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,u.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,u.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center",children:(0,u.jsx)(b,{className:"w-6 h-6 text-white"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Guess My Age"}),(0,u.jsx)("p",{className:"text-sm text-gray-500",children:"AI-powered age estimation"})]})]}),(0,u.jsx)("div",{className:"flex items-center space-x-4",children:l?(0,u.jsx)(F,{onBuyCredits:()=>a(!0)}):(0,u.jsxs)("button",{onClick:()=>{s("signin"),n(!0)},className:"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,u.jsx)(j,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Sign In"})]})})]})})}),(0,u.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,u.jsxs)("div",{className:"flex space-x-1 bg-white rounded-lg p-1 mb-8 shadow-sm max-w-md mx-auto",children:[(0,u.jsxs)("button",{onClick:()=>t("upload"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${"upload"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:[(0,u.jsx)(b,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"Upload Photo"})]}),(0,u.jsxs)("button",{onClick:()=>t("history"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${"history"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:[(0,u.jsx)(E,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:"History"})]})]}),(0,u.jsx)("div",{className:"max-w-4xl mx-auto",children:"upload"===e?(0,u.jsx)(C,{onNeedAuth:f,onNeedCredits:()=>{l?a(!0):f()}}):(0,u.jsx)(_,{})})]}),(0,u.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,u.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsx)("p",{className:"text-gray-600 text-sm",children:"Powered by OpenAI GPT-4o-mini • Built with Next.js and Supabase"}),(0,u.jsx)("p",{className:"text-gray-500 text-xs mt-2",children:"Your photos are analyzed securely and not stored on our servers"})]})})}),(0,u.jsx)(tK,{isOpen:r,onClose:()=>n(!1),initialMode:o}),(0,u.jsx)(rM,{isOpen:i,onClose:()=>a(!1)})]})}},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=o.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),a=r(6341),o=r(4396),s=r(660),l=r(4722),u=r(2958),c=r(5499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function f(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,o.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,a.interpolateDynamicPath)(n,t,s),{name:f,ext:p}=i.default.parse(r),m=d(i.default.posix.join(e,f)),h=m?`-${m}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${f}${h}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},1997:e=>{"use strict";e.exports=require("punycode")},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3567:(e,t,r)=>{"use strict";var n=r(3210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useSyncExternalStore,o=n.useRef,s=n.useEffect,l=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,c){var d=o(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=a(e,(d=l(function(){function e(e){if(!s){if(s=!0,a=e,e=n(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return o=t}return o=e}if(t=o,i(a,e))return t;var r=n(e);return void 0!==c&&c(t,r)?(a=e,t):(a=e,o=r)}var a,o,s=!1,l=void 0===r?null:r;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,r,n,c]))[0],d[1]);return s(function(){f.hasValue=!0,f.value=p},[p]),u(p),p}},3587:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u});var n=r(5239),i=r(8088),a=r(8170),o=r.n(a),s=r(893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:o,searchParams:s,search:l,hash:u,href:c,origin:d}=new URL(e,a);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:u,href:c.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4031:(e,t,r)=>{"use strict";var n=r(4452);function i(){}function a(){}a.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,a,o){if(o!==n){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:i};return r.PropTypes=r,r}},4075:e=>{"use strict";e.exports=require("zlib")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(6143),i=r(1437),a=r(3293),o=r(2887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let d of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),o=d.match(s);if(e&&o&&o[2]){let{key:t,optional:r,repeat:i}=u(o[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=u(o[2]);n[e]={pos:l++,repeat:t,optional:i},r&&o[1]&&c.push("/"+(0,a.escapeStringRegexp)(o[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&o&&o[3]&&c.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=c(e,r,n),s=a;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:o}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:o,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:f}=u(i),p=c.replace(/\W/g,"");s&&(p=""+s+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=n());let h=p in o;s?o[p]=""+s+c:o[p]=c;let g=r?(0,a.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+p+">":f?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let c of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),o=c.match(s);if(e&&o&&o[2])m.push(f({getSafeRouteKey:d,interceptionMarker:o[1],segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){l&&o[1]&&m.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=f({getSafeRouteKey:d,segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&o[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,a.escapeStringRegexp)(c));r&&o&&o[3]&&m.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var r,n,i;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...d(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function h(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>s});var n=r(7413),i=r(5041),a=r.n(i);r(1135);var o=r(9131);let s={title:"Guess My Age - AI Age Estimation",description:"Upload a photo and let AI estimate your age with advanced computer vision technology.",keywords:"age estimation, AI, photo analysis, computer vision"};function l({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().className} antialiased bg-gray-50`,children:(0,n.jsx)(o.AuthProvider,{children:e})})})}},4452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4631:e=>{"use strict";e.exports=require("tls")},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let n=r(5531),i=r(5499);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},4735:e=>{"use strict";e.exports=require("events")},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var s=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,o="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var m=d("CHAR"),h=d("NAME"),g=d("PATTERN");if(h||g){var x=m||"";-1===a.indexOf(x)&&(c+=x,x=""),c&&(s.push(c),c=""),s.push({name:h||l++,prefix:x,suffix:"",pattern:g||o,modifier:d("MODIFIER")||""});continue}var y=m||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(s.push(c),c=""),d("OPEN")){var x=p(),v=d("NAME")||"",b=d("PATTERN")||"",j=p();f("CLOSE"),s.push({name:v||(b?l++:""),pattern:v&&!b?o:b,prefix:x,suffix:j,modifier:d("MODIFIER")||""});continue}f("END")}return s}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<o.length;d++){var f=i(o[d],a);if(s&&!l[n].test(f))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var f=i(String(o),a);if(s&&!l[n].test(f))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:a,index:o,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,r){void 0===r&&(r={});for(var n=r.strict,o=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",f="["+i(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)p+=i(c(h));else{var g=i(c(h.prefix)),x=i(c(h.suffix));if(h.pattern)if(t&&t.push(h),g||x)if("+"===h.modifier||"*"===h.modifier){var y="*"===h.modifier?"?":"";p+="(?:"+g+"((?:"+h.pattern+")(?:"+x+g+"(?:"+h.pattern+"))*)"+x+")"+y}else p+="(?:"+g+"("+h.pattern+")"+x+")"+h.modifier;else p+="("+h.pattern+")"+h.modifier;else p+="(?:"+g+x+")"+h.modifier}}if(void 0===l||l)o||(p+=f+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var v=e[e.length-1],b="string"==typeof v?f.indexOf(v[v.length-1])>-1:void 0===v;o||(p+="(?:"+f+"(?="+d+"))?"),b||(p+="(?="+f+"|"+d+")")}return new RegExp(p,a(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",a(n)):o(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=o,t.pathToRegexp=s})(),e.exports=t})()},5396:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},5511:e=>{"use strict";e.exports=require("crypto")},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return f}});let n=r(5362),i=r(3293),a=r(6759),o=r(1437),s=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let o=r.href;o&&(o=l(o));let s=r.hostname;s&&(s=l(s));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:s,href:o,hash:u}}function f(e){let t,r,i=Object.assign({},e.query),a=d(e),{hostname:s,query:u}=a,f=a.pathname;a.hash&&(f=""+f+a.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(f,m),m))p.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))p.push(t.name)}let h=(0,n.compile)(f,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[r]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=h(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:u,parsedDestination:a}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5591:e=>{"use strict";e.exports=require("https")},5795:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return p}});let n=r(9551),i=r(1959),a=r(2437),o=r(4396),s=r(8034),l=r(5526),u=r(2887),c=r(4722),d=r(6143),f=r(7912);function p(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function m(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:a,repeat:o}=r.groups[n],s=`[${o?"...":""}${n}]`;a&&(s=`[${s}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,i)}return e}function h(e,t,r,n){let i={};for(let a of Object.keys(t.groups)){let o=e[a];"string"==typeof o?o=(0,c.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(c.normalizeRscURL));let s=r[a],l=t.groups[a].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(s))||void 0===o&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${a}]]`))&&(o=void 0,delete e[a]),o&&"string"==typeof o&&t.groups[a].repeat&&(o=o.split("/")),o&&(i[a]=o)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let x,y,v;return c&&(x=(0,o.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(y=(0,s.getRouteMatcher)(x))(e)),{handleRewrites:function(o,s){let f={},p=s.pathname,m=n=>{let u=(0,a.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let m=u(s.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(o,s.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:a,destQuery:o}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:s.query});if(a.protocol)return!0;if(Object.assign(f,o,m),Object.assign(s.query,a.query),delete a.query,Object.assign(s,a),!(p=s.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(p,t.locales);p=e.pathname,s.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(c&&y){let e=y(p);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return f},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,f.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let o=t[a],s=n[e];if(!o.optional&&!s)return null;i[o.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&v?h(e,x,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,x),interpolateDynamicPath:(e,t)=>m(e,t,x)}}function x(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6362:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var l=a[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6558:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>f,A:()=>p});var n=r(687),i=r(3210),a=r(463),o=r(9605);let s=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",l=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",u=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",c=()=>{let e=s(),t=l();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,o.createBrowserClient)(e,t)};(()=>{let e=s(),t=l();if(!e||!t)throw Error("Supabase URL and Anon Key are required");return(0,a.UU)(e,t)})(),(()=>{let e=s(),t=u();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,a.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})})();let d=(0,i.createContext)(void 0);function f({children:e}){let[t,r]=(0,i.useState)(null),[a,o]=(0,i.useState)(null),[s,l]=(0,i.useState)(!0),u=c(),f=async e=>{try{let{data:t,error:r}=await u.from("user_profiles").select("*").eq("id",e).single();if(r)return void console.error("Error fetching profile:",r);o(t)}catch(e){console.error("Error fetching profile:",e)}},p=async()=>{t&&await f(t.id)},m=async(e,t)=>{let{error:r}=await u.auth.signInWithPassword({email:e,password:t});return{error:r}},h=async(e,t)=>{let{error:r}=await u.auth.signUp({email:e,password:t});return{error:r}},g=async()=>{await u.auth.signOut()};return(0,n.jsx)(d.Provider,{value:{user:t,profile:a,loading:s,signIn:m,signUp:h,signOut:g,refreshProfile:p},children:e})}function p(){let e=(0,i.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(2785),i=r(3736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6895:(e,t,r)=>{"use strict";e.exports=r(3567)},7910:e=>{"use strict";e.exports=require("stream")},7955:(e,t,r)=>{e.exports=r(4031)()},7990:()=>{},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?o[e]=r.split("/").map(e=>a(e)):o[e]=a(r))}return o}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let n=r(2958),i=r(4722),a=r(554),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let i=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${o.icon.filename}${a}${l(o.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${o.apple.filename}${a}${l(o.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${o.openGraph.filename}${a}${l(o.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${o.twitter.filename}${a}${l(o.twitter.extensions,t)}${i}`)],u=(0,n.normalizePathSep)(e);return s.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function f(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var n=r(2907);let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\contexts\\AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9410:(e,t,r)=>{Promise.resolve().then(r.bind(r,6558))},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{},9771:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,925],()=>r(3587));module.exports=n})();
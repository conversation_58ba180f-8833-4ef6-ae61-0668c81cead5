(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[493],{306:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},313:(e,t,n)=>{"use strict";n.d(t,{lG:()=>tC});var r,o,i,l,u,a=n(2115),s=n.t(a,2),c=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.<PERSON>="ArrowLeft",e.ArrowUp="ArrowUp",e.<PERSON>Right="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(c||{}),d=Object.defineProperty,f=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t,n)=>(f(e,"symbol"!=typeof t?t+"":t,n),n);class h{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){p(this,"current",this.detect()),p(this,"handoffState","pending"),p(this,"currentId",0)}}let m=new h,v=(e,t)=>{m.isServer?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)};function g(e){let t=(0,a.useRef)(e);return v(()=>{t.current=e},[e]),t}function y(e,t,n,r){let o=g(n);(0,a.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class b extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}function E(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function w(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add(()=>e.removeEventListener(n,r,o))),requestAnimationFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let o=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let o=setTimeout(...n);return t.add(()=>clearTimeout(o))},microTask(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let o={current:!0};return E(()=>{o.current&&n[0]()}),t.add(()=>{o.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(e){let t=w();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}var k=Object.defineProperty,C=(e,t,n)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,S=(e,t,n)=>(C(e,"symbol"!=typeof t?t+"":t,n),n),A=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},P=(e,t,n)=>(A(e,t,"read from private field"),n?n.call(e):t.get(e)),x=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},O=(e,t,n,r)=>(A(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class j{dispose(){this.disposables.dispose()}get state(){return P(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(P(this,r))};return P(this,i).add(n),this.disposables.add(()=>{P(this,i).delete(n)})}on(e,t){return P(this,o).get(e).add(t),this.disposables.add(()=>{P(this,o).get(e).delete(t)})}send(e){let t=this.reduce(P(this,r),e);if(t!==P(this,r)){for(let e of(O(this,r,t),P(this,i))){let t=e.selector(P(this,r));F(e.current,t)||(e.current=t,e.callback(t))}for(let t of P(this,o).get(e.type))t(P(this,r),e)}}constructor(e){x(this,r,{}),x(this,o,new b(()=>new Set)),x(this,i,new Set),S(this,"disposables",w()),O(this,r,e)}}function F(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&T(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&T(e.entries(),t.entries()):!!(R(e)&&R(t))&&T(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function T(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function R(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function N(e,t){for(var n=arguments.length,r=Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,N),i}r=new WeakMap,o=new WeakMap,i=new WeakMap;var L=Object.defineProperty,M=(e,t,n)=>t in e?L(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,D=(e,t,n)=>(M(e,"symbol"!=typeof t?t+"":t,n),n),I=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(I||{});let _={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class U extends j{static new(){return new U({stack:[]})}reduce(e,t){return N(t.type,_,e,t)}constructor(){super(...arguments),D(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),D(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}}let W=new b(()=>U.new());var B=n(1992);let H=function(e){let t=g(e);return a.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])};function Y(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:F;return(0,B.useSyncExternalStoreWithSelector)(H(t=>e.subscribe(q,t)),H(()=>e.state),H(()=>e.state),H(t),n)}function q(e){return e}function V(e,t){let n=(0,a.useId)(),r=W.get(t),[o,i]=Y(r,(0,a.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return v(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!i||o)}function z(e){var t,n;return m.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let G=new Map,K=new Map;function X(e){var t;let n=null!=(t=K.get(e))?t:0;return K.set(e,n+1),0!==n||(G.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=K.get(e))?t:1;if(1===n?K.delete(e):K.set(e,n-1),1!==n)return;let r=G.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,G.delete(e))})(e)}function $(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function Z(e){return $(e)&&"tagName"in e}function J(e){return Z(e)&&"accessKey"in e}function Q(e){return Z(e)&&"tabIndex"in e}let ee=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),et=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var en=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(en||{}),er=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(er||{}),eo=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(eo||{}),ei=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ei||{}),el=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(el||{});function eu(e){null==e||e.focus({preventScroll:!0})}function ea(e,t){var n,r,o;let{sorted:i=!0,relativeTo:l=null,skipElements:u=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?i?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(et)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(ee)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);u.length>0&&s.length>1&&(s=s.filter(e=>!u.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),l=null!=l?l:a.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(l))-1;if(4&t)return Math.max(0,s.indexOf(l))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,h=s.length,m;do{if(p>=h||p+h<=0)return 0;let e=d+p;if(16&t)e=(e+h)%h;else{if(e<0)return 3;if(e>=h)return 1}null==(m=s[e])||m.focus(f),p+=c}while(m!==a.activeElement);return 6&t&&null!=(o=null==(r=null==(n=m)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&o&&m.select(),2}function es(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function ec(){return es()||/Android/gi.test(window.navigator.userAgent)}function ed(e,t,n,r){let o=g(n);(0,a.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function ef(e,t,n,r){let o=g(n);(0,a.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function ep(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.useMemo)(()=>z(...t),[...t])}function eh(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var em=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(em||{}),ev=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ev||{});function eg(){let e,t,n=(e=(0,a.useRef)([]),t=(0,a.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t});return(0,a.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:i,visible:l=!0,name:u,mergeRefs:a}=e;a=null!=a?a:eb;let s=eE(n,t);if(l)return ey(s,r,o,u,a);let c=null!=i?i:0;if(2&c){let{static:e=!1,...t}=s;if(e)return ey(t,r,o,u,a)}if(1&c){let{unmount:e=!0,...t}=s;return N(+!e,{0:()=>null,1:()=>ey({...t,hidden:!0,style:{display:"none"}},r,o,u,a)})}return ey(s,r,o,u,a)})({mergeRefs:n,...e}),[n])}function ey(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,{as:i=n,children:l,refName:u="ref",...s}=eC(e,["unmount","static"]),c=void 0!==e.ref?{[u]:e.ref}:{},d="function"==typeof l?l(t):l;"className"in s&&s.className&&"function"==typeof s.className&&(s.className=s.className(t)),s["aria-labelledby"]&&s["aria-labelledby"]===s.id&&(s["aria-labelledby"]=void 0);let f={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(f["data-headlessui-state"]=n.join(" "),n))f["data-".concat(e)]=""}if(i===a.Fragment&&(Object.keys(ek(s)).length>0||Object.keys(ek(f)).length>0))if(!(0,a.isValidElement)(d)||Array.isArray(d)&&d.length>1){if(Object.keys(ek(s)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(r,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(ek(s)).concat(Object.keys(ek(f))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var p;let e=d.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return eh(t(...n),s.className)}:eh(t,s.className),r=eE(d.props,ek(eC(s,["ref"])));for(let e in f)e in r&&delete f[e];return(0,a.cloneElement)(d,Object.assign({},r,f,c,{ref:o((p=d,a.version.split(".")[0]>="19"?p.props.ref:p.ref),c.ref)},n?{className:n}:{}))}return(0,a.createElement)(i,Object.assign({},eC(s,["ref"]),i!==a.Fragment&&c,i!==a.Fragment&&f),d)}function eb(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function eE(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(o[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function ew(e){var t;return Object.assign((0,a.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function ek(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function eC(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}var eS=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(eS||{});let eA=ew(function(e,t){var n;let{features:r=1,...o}=e,i={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return eg()({ourProps:i,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),eP=(0,a.createContext)(null);function ex(e){let{children:t,node:n}=e,[r,o]=(0,a.useState)(null),i=eO(null!=n?n:r);return a.createElement(eP.Provider,{value:i},t,null===i&&a.createElement(eA,{features:eS.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=z(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&Z(r)&&null!=r&&r.contains(e)){o(r);break}}}}))}function eO(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!=(e=(0,a.useContext)(eP))?e:t}let ej=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,i=Array(o>1?o-1:0),l=1;l<o;l++)i[l-1]=arguments[l];let u=t[e].call(n,...i);u&&(n=u,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:w(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:o}=e,i={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(o)},l=[es()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=w();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,i=null;n.addEventListener(t,"click",e=>{if(Q(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),l=t.querySelector(r);Q(l)&&!o(l)&&(i=l)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{var t;if(Q(e.target)&&Z(t=e.target)&&"style"in t)if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(Q(e.target)){var t;if(!(J(t=e.target)&&"INPUT"===t.nodeName))if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},{before(e){var n;let{doc:r}=e,o=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-o.clientWidth)},after(e){let{doc:n,d:r}=e,o=n.documentElement,i=Math.max(0,o.clientWidth-o.offsetWidth),l=Math.max(0,t-i);r.style(o,"paddingRight","".concat(l,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];l.forEach(e=>{let{before:t}=e;return null==t?void 0:t(i)}),l.forEach(e=>{let{after:t}=e;return null==t?void 0:t(i)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});function eF(){let e,t=(e="undefined"==typeof document,(0,s.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[n,r]=a.useState(m.isHandoffComplete);return n&&!1===m.isHandoffComplete&&r(!1),a.useEffect(()=>{!0!==n&&r(!0)},[n]),a.useEffect(()=>m.handoff(),[]),!t&&n}ej.subscribe(()=>{let e=ej.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&ej.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&ej.dispatch("TEARDOWN",n)}});let eT=Symbol();function eR(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=(0,a.useRef)(t);(0,a.useEffect)(()=>{r.current=t},[t]);let o=H(e=>{for(let t of r.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[eT]))?void 0:o}let eN=(0,a.createContext)(()=>{});function eL(e){let{value:t,children:n}=e;return a.createElement(eN.Provider,{value:t},n)}let eM=(0,a.createContext)(null);eM.displayName="OpenClosedContext";var eD=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(eD||{});function eI(){return(0,a.useContext)(eM)}function e_(e){let{value:t,children:n}=e;return a.createElement(eM.Provider,{value:t},n)}function eU(e){let{children:t}=e;return a.createElement(eM.Provider,{value:null},t)}let eW=(0,a.createContext)(!1);function eB(e){return a.createElement(eW.Provider,{value:e.force},e.children)}let eH=(0,a.createContext)(void 0),eY=(0,a.createContext)(null);eY.displayName="DescriptionContext";let eq=Object.assign(ew(function(e,t){let n=(0,a.useId)(),r=(0,a.useContext)(eH),{id:o="headlessui-description-".concat(n),...i}=e,l=function e(){let t=(0,a.useContext)(eY);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=eR(t);v(()=>l.register(o),[o,l.register]);let s=r||!1,c=(0,a.useMemo)(()=>({...l.slot,disabled:s}),[l.slot,s]),d={ref:u,...l.props,id:o};return eg()({ourProps:d,theirProps:i,slot:c,defaultTag:"p",name:l.name||"Description"})}),{});function eV(){let[e]=(0,a.useState)(w);return(0,a.useEffect)(()=>()=>e.dispose(),[e]),e}function ez(){let e=(0,a.useRef)(!1);return v(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function eG(e){let t=H(e),n=(0,a.useRef)(!1);(0,a.useEffect)(()=>(n.current=!1,()=>{n.current=!0,E(()=>{n.current&&t()})}),[t])}var eK=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(eK||{});function eX(e,t){let n=(0,a.useRef)([]),r=H(e);(0,a.useEffect)(()=>{let e=[...n.current];for(let[o,i]of t.entries())if(n.current[o]!==i){let o=r(t,e);return n.current=t,o}},[r,...t])}let e$=[];function eZ(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)Z(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!Q(e.target)||e.target===document.body||e$[0]===e.target)return;let t=e.target;t=t.closest(ee),e$.unshift(null!=t?t:e.target),(e$=e$.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eJ=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eJ||{});let eQ=Object.assign(ew(function(e,t){let n,r=(0,a.useRef)(null),o=eR(r,t),{initialFocus:i,initialFocusFallback:l,containers:u,features:s=15,...c}=e;eF()||(s=0);let d=ep(r);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),o=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,a.useRef)(e$.slice());return eX((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&E(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=e$.slice())},[e,e$,t]),H(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eX(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&eu(o())},[r]),eG(()=>{r&&eu(o())})}(s,{ownerDocument:d});let f=function(e,t){let{ownerDocument:n,container:r,initialFocus:o,initialFocusFallback:i}=t,l=(0,a.useRef)(null),u=V(!!(1&e),"focus-trap#initial-focus"),s=ez();return eX(()=>{if(0===e)return;if(!u){null!=i&&i.current&&eu(i.current);return}let t=r.current;t&&E(()=>{if(!s.current)return;let r=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===r){l.current=r;return}}else if(t.contains(r)){l.current=r;return}if(null!=o&&o.current)eu(o.current);else{if(16&e){if(ea(t,en.First|en.AutoFocus)!==er.Error)return}else if(ea(t,en.First)!==er.Error)return;if(null!=i&&i.current&&(eu(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}l.current=null==n?void 0:n.activeElement})},[i,u,e]),l}(s,{ownerDocument:d,container:r,initialFocus:i,initialFocusFallback:l});!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:i}=t,l=ez(),u=!!(4&e);y(null==n?void 0:n.defaultView,"focus",e=>{if(!u||!l.current)return;let t=eZ(o);J(r.current)&&t.add(r.current);let n=i.current;if(!n)return;let a=e.target;J(a)?e0(t,a)?(i.current=a,eu(a)):(e.preventDefault(),e.stopPropagation(),eu(n)):eu(i.current)},!0)}(s,{ownerDocument:d,container:r,containers:u,previousActiveElement:f});let p=(n=(0,a.useRef)(0),ef(!0,"keydown",e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},!0),n),h=H(e=>{if(!J(r.current))return;let t=r.current;N(p.current,{[eK.Forwards]:()=>{ea(t,en.First,{skipElements:[e.relatedTarget,l]})},[eK.Backwards]:()=>{ea(t,en.Last,{skipElements:[e.relatedTarget,l]})}})}),m=V(!!(2&s),"focus-trap#tab-lock"),v=eV(),g=(0,a.useRef)(!1),b=eg();return a.createElement(a.Fragment,null,m&&a.createElement(eA,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:eS.Focusable}),b({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(g.current=!0,v.requestAnimationFrame(()=>{g.current=!1}))},onBlur(e){if(!(4&s))return;let t=eZ(u);J(r.current)&&t.add(r.current);let n=e.relatedTarget;Q(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(e0(t,n)||(g.current?ea(r.current,N(p.current,{[eK.Forwards]:()=>en.Next,[eK.Backwards]:()=>en.Previous})|en.WrapAround,{relativeTo:e.target}):Q(e.target)&&eu(e.target)))}},theirProps:c,defaultTag:"div",name:"FocusTrap"}),m&&a.createElement(eA,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:eS.Focusable}))}),{features:eJ});function e0(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var e1=n(7650);let e2=a.Fragment,e4=ew(function(e,t){let{ownerDocument:n=null,...r}=e,o=(0,a.useRef)(null),i=eR(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[eT]:t})}(e=>{o.current=e}),t),l=ep(o),u=null!=n?n:l,s=function(e){let t=(0,a.useContext)(eW),n=(0,a.useContext)(e3),[r,o]=(0,a.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(m.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let i=e.createElement("div");return i.setAttribute("id","headlessui-portal-root"),e.body.appendChild(i)});return(0,a.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,a.useEffect)(()=>{t||null!==n&&o(n.current)},[n,o,t]),r}(u),[c]=(0,a.useState)(()=>{var e;return m.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),d=(0,a.useContext)(e6),f=eF();v(()=>{!s||!c||s.contains(c)||(c.setAttribute("data-headlessui-portal",""),s.appendChild(c))},[s,c]),v(()=>{if(c&&d)return d.register(c)},[d,c]),eG(()=>{var e;s&&c&&($(c)&&s.contains(c)&&s.removeChild(c),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))});let p=eg();return f&&s&&c?(0,e1.createPortal)(p({ourProps:{ref:i},theirProps:r,slot:{},defaultTag:e2,name:"Portal"}),c):null}),e9=a.Fragment,e3=(0,a.createContext)(null),e6=(0,a.createContext)(null),e5=ew(function(e,t){let n=eR(t),{enabled:r=!0,ownerDocument:o,...i}=e,l=eg();return r?a.createElement(e4,{...i,ownerDocument:o,ref:n}):l({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:e2,name:"Portal"})}),e7=ew(function(e,t){let{target:n,...r}=e,o={ref:eR(t)},i=eg();return a.createElement(e3.Provider,{value:n},i({ourProps:o,theirProps:r,defaultTag:e9,name:"Popover.Group"}))}),e8=Object.assign(e5,{Group:e7});var te=n(9509);void 0!==te&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(l=null==te?void 0:te.env)?void 0:l.NODE_ENV)==="test"&&void 0===(null==(u=null==Element?void 0:Element.prototype)?void 0:u.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var tt=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(tt||{});function tn(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:ta)!==a.Fragment||1===a.Children.count(e.children)}let tr=(0,a.createContext)(null);tr.displayName="TransitionContext";var to=(e=>(e.Visible="visible",e.Hidden="hidden",e))(to||{});let ti=(0,a.createContext)(null);function tl(e){return"children"in e?tl(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function tu(e,t){let n=g(e),r=(0,a.useRef)([]),o=ez(),i=eV(),l=H(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ev.Hidden,l=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==l&&(N(t,{[ev.Unmount](){r.current.splice(l,1)},[ev.Hidden](){r.current[l].state="hidden"}}),i.microTask(()=>{var e;!tl(r)&&o.current&&(null==(e=n.current)||e.call(n))}))}),u=H(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>l(e,ev.Unmount)}),s=(0,a.useRef)([]),c=(0,a.useRef)(Promise.resolve()),d=(0,a.useRef)({enter:[],leave:[]}),f=H((e,n,r)=>{s.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{s.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(d.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?c.current=c.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),p=H((e,t,n)=>{Promise.all(d.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=s.current.shift())||e()}).then(()=>n(t))});return(0,a.useMemo)(()=>({children:r,register:u,unregister:l,onStart:f,onStop:p,wait:c,chains:d}),[u,l,r,f,p,d,c])}ti.displayName="NestingContext";let ta=a.Fragment,ts=em.RenderStrategy,tc=ew(function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...i}=e,l=(0,a.useRef)(null),u=eR(...tn(e)?[l,t]:null===t?[]:[t]);eF();let s=eI();if(void 0===n&&null!==s&&(n=(s&eD.Open)===eD.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,d]=(0,a.useState)(n?"visible":"hidden"),f=tu(()=>{n||d("hidden")}),[p,h]=(0,a.useState)(!0),m=(0,a.useRef)([n]);v(()=>{!1!==p&&m.current[m.current.length-1]!==n&&(m.current.push(n),h(!1))},[m,n]);let g=(0,a.useMemo)(()=>({show:n,appear:r,initial:p}),[n,r,p]);v(()=>{n?d("visible"):tl(f)||null===l.current||d("hidden")},[n,f]);let y={unmount:o},b=H(()=>{var t;p&&h(!1),null==(t=e.beforeEnter)||t.call(e)}),E=H(()=>{var t;p&&h(!1),null==(t=e.beforeLeave)||t.call(e)}),w=eg();return a.createElement(ti.Provider,{value:f},a.createElement(tr.Provider,{value:g},w({ourProps:{...y,as:a.Fragment,children:a.createElement(td,{ref:u,...y,...i,beforeEnter:b,beforeLeave:E})},theirProps:{},defaultTag:a.Fragment,features:ts,visible:"visible"===c,name:"Transition"})))}),td=ew(function(e,t){var n,r;let{transition:o=!0,beforeEnter:i,afterEnter:l,beforeLeave:u,afterLeave:s,enter:c,enterFrom:d,enterTo:f,entered:p,leave:h,leaveFrom:m,leaveTo:g,...y}=e,[b,E]=(0,a.useState)(null),k=(0,a.useRef)(null),C=tn(e),S=eR(...C?[k,t,E]:null===t?[]:[t]),A=null==(n=y.unmount)||n?ev.Unmount:ev.Hidden,{show:P,appear:x,initial:O}=function(){let e=(0,a.useContext)(tr);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[j,F]=(0,a.useState)(P?"visible":"hidden"),T=function(){let e=(0,a.useContext)(ti);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:R,unregister:L}=T;v(()=>R(k),[R,k]),v(()=>{if(A===ev.Hidden&&k.current)return P&&"visible"!==j?void F("visible"):N(j,{hidden:()=>L(k),visible:()=>R(k)})},[j,k,R,L,P,A]);let M=eF();v(()=>{if(C&&M&&"visible"===j&&null===k.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[k,j,M,C]);let D=O&&!x,I=x&&P&&O,_=(0,a.useRef)(!1),U=tu(()=>{_.current||(F("hidden"),L(k))},T),W=H(e=>{_.current=!0,U.onStart(k,e?"enter":"leave",e=>{"enter"===e?null==i||i():"leave"===e&&(null==u||u())})}),B=H(e=>{let t=e?"enter":"leave";_.current=!1,U.onStop(k,t,e=>{"enter"===e?null==l||l():"leave"===e&&(null==s||s())}),"leave"!==t||tl(U)||(F("hidden"),L(k))});(0,a.useEffect)(()=>{C&&o||(W(P),B(P))},[P,C,o]);let[,Y]=function(e,t,n,r){let[o,i]=(0,a.useState)(n),{hasFlag:l,addFlag:u,removeFlag:s}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,a.useState)(e),r=(0,a.useCallback)(e=>n(e),[t]),o=(0,a.useCallback)(e=>n(t=>t|e),[t]),i=(0,a.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:(0,a.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,a.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),c=(0,a.useRef)(!1),d=(0,a.useRef)(!1);return v(()=>{var o;if(e){if(n&&i(!0),!t){n&&u(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,t){let{prepare:n,run:r,done:o,inFlight:i}=t,l=w();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:i}),l.nextFrame(()=>{r(),l.requestAnimationFrame(()=>{l.add(function(e,t){var n,r;let o=w();if(!e)return o.dispose;let i=!1;o.add(()=>{i=!0});let l=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===l.length?t():Promise.allSettled(l.map(e=>e.finished)).then(()=>{i||t()}),o.dispose}(e,o))})}),l.dispose}(t,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,d.current||(n?(u(3),s(4)):(u(4),s(2)))},run(){d.current?n?(s(3),u(4)):(s(4),u(3)):n?s(1):u(1)},done(){var e;d.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(c.current=!1,s(7),n||i(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,eV()]),e?[o,{closed:l(1),enter:l(2),leave:l(4),transition:l(2)||l(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!o||!C||!M||D),b,P,{start:W,end:B}),q=ek({ref:S,className:(null==(r=eh(y.className,I&&c,I&&d,Y.enter&&c,Y.enter&&Y.closed&&d,Y.enter&&!Y.closed&&f,Y.leave&&h,Y.leave&&!Y.closed&&m,Y.leave&&Y.closed&&g,!Y.transition&&P&&p))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(Y)}),V=0;"visible"===j&&(V|=eD.Open),"hidden"===j&&(V|=eD.Closed),P&&"hidden"===j&&(V|=eD.Opening),P||"visible"!==j||(V|=eD.Closing);let z=eg();return a.createElement(ti.Provider,{value:U},a.createElement(e_,{value:V},z({ourProps:q,theirProps:y,defaultTag:ta,features:ts,visible:"visible"===j,name:"Transition.Child"})))}),tf=ew(function(e,t){let n=null!==(0,a.useContext)(tr),r=null!==eI();return a.createElement(a.Fragment,null,!n&&r?a.createElement(tc,{ref:t,...e}):a.createElement(td,{ref:t,...e}))}),tp=Object.assign(tc,{Child:tf,Root:tc});var th=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(th||{}),tm=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(tm||{});let tv={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},tg=(0,a.createContext)(null);function ty(e){let t=(0,a.useContext)(tg);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,ty),t}return t}function tb(e,t){return N(t.type,tv,e,t)}tg.displayName="DialogContext";let tE=ew(function(e,t){let n,r,o,i,l,u,s,d,f,p,h=(0,a.useId)(),{id:m="headlessui-dialog-".concat(h),open:b,onClose:E,initialFocus:k,role:C="dialog",autoFocus:S=!0,__demoMode:A=!1,unmount:P=!1,...x}=e,O=(0,a.useRef)(!1);C="dialog"===C||"alertdialog"===C?C:(O.current||(O.current=!0,console.warn("Invalid role [".concat(C,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let j=eI();void 0===b&&null!==j&&(b=(j&eD.Open)===eD.Open);let F=(0,a.useRef)(null),T=eR(F,t),R=ep(F),L=+!b,[M,D]=(0,a.useReducer)(tb,{titleId:null,descriptionId:null,panelRef:(0,a.createRef)()}),I=H(()=>E(!1)),_=H(e=>D({type:0,id:e})),U=!!eF()&&0===L,[B,q]=(n=(0,a.useContext)(e6),r=(0,a.useRef)([]),o=H(e=>(r.current.push(e),n&&n.register(e),()=>i(e))),i=H(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),l=(0,a.useMemo)(()=>({register:o,unregister:i,portals:r}),[o,i,r]),[r,(0,a.useMemo)(()=>function(e){let{children:t}=e;return a.createElement(e6.Provider,{value:l},t)},[l])]),G=eO(),{resolveContainers:K}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=ep(n),o=H(()=>{var o,i;let l=[];for(let t of e)null!==t&&(Z(t)?l.push(t):"current"in t&&Z(t.current)&&l.push(t.current));if(null!=t&&t.current)for(let e of t.current)l.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&Z(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(i=null==n?void 0:n.getRootNode())?void 0:i.host))||l.some(t=>e.contains(t))||l.push(e));return l});return{resolveContainers:o,contains:H(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:G,portals:B,defaultContainers:[{get current(){var $;return null!=($=M.panelRef.current)?$:F.current}}]}),et=null!==j&&(j&eD.Closing)===eD.Closing;!function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=V(e,"inert-others");v(()=>{var e,o;if(!r)return;let i=w();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&i.add(X(t));let l=null!=(o=null==t?void 0:t())?o:[];for(let e of l){if(!e)continue;let t=z(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)l.some(t=>e.contains(t))||i.add(X(e));n=n.parentElement}}return i.dispose},[r,t,n])}(!A&&!et&&U,{allowed:H(()=>{var e,t;return[null!=(t=null==(e=F.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:H(()=>{var e;return[null!=(e=null==G?void 0:G.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let en=W.get(null);v(()=>{if(U)return en.actions.push(m),()=>en.actions.pop(m)},[en,m,U]);let er=Y(en,(0,a.useCallback)(e=>en.selectors.isTop(e,m),[en,m]));u=g(e=>{e.preventDefault(),I()}),s=(0,a.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(K))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=z(e))?void 0:t.body)&&N(n,{0:()=>e.matches(ee),1(){let t=e;for(;null!==t;){if(t.matches(ee))return!0;t=t.parentElement}return!1}})}(n,ei.Loose)||-1===n.tabIndex||e.preventDefault(),u.current(e,n)}},[u,K]),d=(0,a.useRef)(null),ed(er,"pointerdown",e=>{var t,n;ec()||(d.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ed(er,"pointerup",e=>{if(ec()||!d.current)return;let t=d.current;return d.current=null,s(e,()=>t)},!0),f=(0,a.useRef)({x:0,y:0}),ed(er,"touchstart",e=>{f.current.x=e.touches[0].clientX,f.current.y=e.touches[0].clientY},!0),ed(er,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-f.current.x)>=30||Math.abs(t.y-f.current.y)>=30))return s(e,()=>Q(e.target)?e.target:null)},!0),ef(er,"blur",e=>s(e,()=>{var e;return J(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=V(e,"escape");y(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===c.Escape&&n(e))})}(er,null==R?void 0:R.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),I()}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=(0,a.useSyncExternalStore)(ej.subscribe,ej.getSnapshot,ej.getSnapshot),o=t?r.get(t):void 0;o&&o.count,v(()=>{if(!(!t||!e))return ej.dispatch("PUSH",t,n),()=>ej.dispatch("POP",t,n)},[e,t])}(V(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!A&&!et&&U,R,K),p=g(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&I()}),(0,a.useEffect)(()=>{if(!U)return;let e=null===F?null:J(F)?F:F.current;if(!e)return;let t=w();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>p.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>p.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[F,p,U]);let[eo,el]=function(){let[e,t]=(0,a.useState)([]);return[e.length>0?e.join(" "):void 0,(0,a.useMemo)(()=>function(e){let n=H(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,a.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return a.createElement(eY.Provider,{value:r},e.children)},[t])]}(),eu=(0,a.useMemo)(()=>[{dialogState:L,close:I,setTitleId:_,unmount:P},M],[L,M,I,_,P]),ea=(0,a.useMemo)(()=>({open:0===L}),[L]),es={ref:T,id:m,role:C,tabIndex:-1,"aria-modal":A?void 0:0===L||void 0,"aria-labelledby":M.titleId,"aria-describedby":eo,unmount:P},eh=!function(){var e;let[t]=(0,a.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,a.useState)(null!=(e=null==t?void 0:t.matches)&&e);return v(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),em=eJ.None;U&&!A&&(em|=eJ.RestoreFocus,em|=eJ.TabLock,S&&(em|=eJ.AutoFocus),eh&&(em|=eJ.InitialFocus));let ev=eg();return a.createElement(eU,null,a.createElement(eB,{force:!0},a.createElement(e8,null,a.createElement(tg.Provider,{value:eu},a.createElement(e7,{target:F},a.createElement(eB,{force:!1},a.createElement(el,{slot:ea},a.createElement(q,null,a.createElement(eQ,{initialFocus:k,initialFocusFallback:F,containers:K,features:em},a.createElement(eL,{value:I},ev({ourProps:es,theirProps:x,slot:ea,defaultTag:tw,features:tk,visible:0===L,name:"Dialog"})))))))))))}),tw="div",tk=em.RenderStrategy|em.Static,tC=Object.assign(ew(function(e,t){let{transition:n=!1,open:r,...o}=e,i=eI(),l=e.hasOwnProperty("open")||null!==i,u=e.hasOwnProperty("onClose");if(!l&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!l)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==r||n)&&!o.static?a.createElement(ex,null,a.createElement(tp,{show:r,transition:n,unmount:o.unmount},a.createElement(tE,{ref:t,...o}))):a.createElement(ex,null,a.createElement(tE,{ref:t,open:r,...o}))}),{Panel:ew(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:o=!1,...i}=e,[{dialogState:l,unmount:u},s]=ty("Dialog.Panel"),c=eR(t,s.panelRef),d=(0,a.useMemo)(()=>({open:0===l}),[l]),f=H(e=>{e.stopPropagation()}),p=o?tf:a.Fragment,h=eg();return a.createElement(p,{...o?{unmount:u}:{}},h({ourProps:{ref:c,id:r,onClick:f},theirProps:i,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:(ew(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:o,unmount:i}]=ty("Dialog.Backdrop"),l=(0,a.useMemo)(()=>({open:0===o}),[o]),u=n?tf:a.Fragment,s=eg();return a.createElement(u,{...n?{unmount:i}:{}},s({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:l,defaultTag:"div",name:"Dialog.Backdrop"}))}),ew(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-dialog-title-".concat(n),...o}=e,[{dialogState:i,setTitleId:l}]=ty("Dialog.Title"),u=eR(t);(0,a.useEffect)(()=>(l(r),()=>l(null)),[r,l]);let s=(0,a.useMemo)(()=>({open:0===i}),[i]);return eg()({ourProps:{ref:u,id:r},theirProps:o,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Description:eq})},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1992:(e,t,n)=>{"use strict";e.exports=n(4993)},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2919:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4186:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},4993:(e,t,n)=>{"use strict";var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useSyncExternalStore,l=r.useRef,u=r.useEffect,a=r.useMemo,s=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=i(e,(d=a(function(){function e(e){if(!u){if(u=!0,i=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return l=t}return l=e}if(t=l,o(i,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(i=e,t):(i=e,l=n)}var i,l,u=!1,a=void 0===n?null:n;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,n,r,c]))[0],d[1]);return u(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},5196:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5855:function(e,t,n){(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var i=[],l=!0,u=!1;try{for(o=o.call(e);!(l=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);l=!0);}catch(e){u=!0,r=e}finally{try{l||null==o.return||o.return()}finally{if(u)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var s,c,d,f,p,h={exports:{}};h.exports=(function(){if(p)return f;p=1;var e=d?c:(d=1,c="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,f=function(){function r(t,n,r,o,i,l){if(l!==e){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function o(){return r}r.isRequired=r;var i={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return i.PropTypes=i,i}})()();var m=(s=h.exports)&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s,v=function(e,n,r){var o=!!r,i=t.useRef(r);t.useEffect(function(){i.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){i.current&&i.current.apply(i,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,i])},g=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},y=function(e){return null!==e&&"object"===o(e)},b="[object Object]",E=function e(t,n){if(!y(t)||!y(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===b;if(o!==(Object.prototype.toString.call(n)===b))return!1;if(!o&&!r)return t===n;var i=Object.keys(t),l=Object.keys(n);if(i.length!==l.length)return!1;for(var u={},a=0;a<i.length;a+=1)u[i[a]]=!0;for(var s=0;s<l.length;s+=1)u[l[s]]=!0;var c=Object.keys(u);return c.length===i.length&&c.every(function(r){return e(t[r],n[r])})},w=function(e,t,n){return y(e)?Object.keys(e).reduce(function(o,l){var u=!y(t)||!E(e[l],t[l]);return n.includes(l)?(u&&console.warn("Unsupported prop change: options.".concat(l," is not a mutable property.")),o):u?r(r({},o||{}),{},i({},l,e[l])):o},null):null},k="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",C=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(null===e||y(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(y(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return C(e,t)})};var n=C(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},A=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},P=t.createContext(null);P.displayName="ElementsContext";var x=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},O=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return S(n)},[n]),l=u(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),a=l[0],s=l[1];t.useEffect(function(){var e=!0,t=function(e){s(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||a.stripe?"sync"!==i.tag||a.stripe||t(i.stripe):i.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[i,a,r]);var c=g(n);t.useEffect(function(){null!==c&&c!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[c,n]);var d=g(r);return t.useEffect(function(){if(a.elements){var e=w(r,d,["clientSecret","fonts"]);e&&a.elements.update(e)}},[r,d,a.elements]),t.useEffect(function(){A(a.stripe)},[a.stripe]),t.createElement(P.Provider,{value:a},o)};O.propTypes={stripe:m.any,options:m.object};var j=function(e){return x(t.useContext(P),e)},F=function(e){return(0,e.children)(j("mounts <ElementsConsumer>"))};F.propTypes={children:m.func.isRequired};var T=["on","session"],R=t.createContext(null);R.displayName="CheckoutSdkContext";var N=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},L=t.createContext(null);L.displayName="CheckoutContext";var M=function(e,t){if(!e)return null;e.on,e.session;var n=l(e,T);return t?Object.assign(t,n):Object.assign(e.session(),n)},D=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return S(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),l=u(t.useState(null),2),a=l[0],s=l[1],c=u(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,checkoutSdk:null}}),2),d=c[0],f=c[1],p=function(e,t){f(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},h=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==i.tag||d.stripe?"sync"===i.tag&&i.stripe&&!h.current&&(h.current=!0,i.stripe.initCheckout(r).then(function(e){e&&(p(i.stripe,e),e.on("change",s))})):i.stripePromise.then(function(t){t&&e&&!h.current&&(h.current=!0,t.initCheckout(r).then(function(e){e&&(p(t,e),e.on("change",s))}))}),function(){e=!1}},[i,d,r,s]);var m=g(n);t.useEffect(function(){null!==m&&m!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[m,n]);var v=g(r),y=g(d.checkoutSdk);t.useEffect(function(){if(d.checkoutSdk){var e,t,n=null==v||null==(e=v.elementsOptions)?void 0:e.appearance,o=null==r||null==(t=r.elementsOptions)?void 0:t.appearance,i=!E(o,n),l=!y&&d.checkoutSdk;o&&(i||l)&&d.checkoutSdk.changeAppearance(o)}},[r,v,d.checkoutSdk,y]),t.useEffect(function(){A(d.stripe)},[d.stripe]);var b=t.useMemo(function(){return M(d.checkoutSdk,a)},[d.checkoutSdk,a]);return d.checkoutSdk?t.createElement(R.Provider,{value:d},t.createElement(L.Provider,{value:b},o)):null};D.propTypes={stripe:m.any,options:m.shape({fetchClientSecret:m.func.isRequired,elementsOptions:m.object}).isRequired};var I=function(e){var n=t.useContext(R),r=t.useContext(P);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?N(n,e):x(r,e)},_=["mode"],U=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){I("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,i=n.id,a=n.className,s=n.options,c=void 0===s?{}:s,d=n.onBlur,f=n.onFocus,p=n.onReady,h=n.onChange,m=n.onEscape,y=n.onClick,b=n.onLoadError,E=n.onLoaderStart,k=n.onNetworksChange,C=n.onConfirm,S=n.onCancel,A=n.onShippingAddressChange,P=n.onShippingRateChange,x=I("mounts <".concat(r,">")),O="elements"in x?x.elements:null,j="checkoutSdk"in x?x.checkoutSdk:null,F=u(t.useState(null),2),T=F[0],R=F[1],N=t.useRef(null),L=t.useRef(null);v(T,"blur",d),v(T,"focus",f),v(T,"escape",m),v(T,"click",y),v(T,"loaderror",b),v(T,"loaderstart",E),v(T,"networkschange",k),v(T,"confirm",C),v(T,"cancel",S),v(T,"shippingaddresschange",A),v(T,"shippingratechange",P),v(T,"change",h),p&&(o="expressCheckout"===e?p:function(){p(T)}),v(T,"ready",o),t.useLayoutEffect(function(){if(null===N.current&&null!==L.current&&(O||j)){var t=null;if(j)switch(e){case"payment":t=j.createPaymentElement(c);break;case"address":if("mode"in c){var n=c.mode,o=l(c,_);if("shipping"===n)t=j.createShippingAddressElement(o);else if("billing"===n)t=j.createBillingAddressElement(o);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=j.createExpressCheckoutElement(c);break;case"currencySelector":t=j.createCurrencySelectorElement();break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else O&&(t=O.create(e,c));N.current=t,R(t),t&&t.mount(L.current)}},[O,j,c]);var M=g(c);return t.useEffect(function(){if(N.current){var e=w(c,M,["paymentRequest"]);e&&"update"in N.current&&N.current.update(e)}},[c,M]),t.useLayoutEffect(function(){return function(){if(N.current&&"function"==typeof N.current.destroy)try{N.current.destroy(),N.current=null}catch(e){}}},[]),t.createElement("div",{id:i,className:a,ref:L})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},W="undefined"==typeof window,B=t.createContext(null);B.displayName="EmbeddedCheckoutProviderContext";var H=function(){var e=t.useContext(B);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},Y=W?function(e){var n=e.id,r=e.className;return H(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=H().embeddedCheckout,i=t.useRef(!1),l=t.useRef(null);return t.useLayoutEffect(function(){return!i.current&&o&&null!==l.current&&(o.mount(l.current),i.current=!0),function(){if(i.current&&o)try{o.unmount(),i.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:l,id:n,className:r})},q=U("auBankAccount",W),V=U("card",W),z=U("cardNumber",W),G=U("cardExpiry",W),K=U("cardCvc",W),X=U("fpxBank",W),$=U("iban",W),Z=U("idealBank",W),J=U("p24Bank",W),Q=U("epsBank",W),ee=U("payment",W),et=U("expressCheckout",W),en=U("currencySelector",W),er=U("paymentRequestButton",W),eo=U("linkAuthentication",W),ei=U("address",W),el=U("shippingAddress",W),eu=U("paymentMethodMessaging",W),ea=U("affirmMessage",W),es=U("afterpayClearpayMessage",W);e.AddressElement=ei,e.AffirmMessageElement=ea,e.AfterpayClearpayMessageElement=es,e.AuBankAccountElement=q,e.CardCvcElement=K,e.CardElement=V,e.CardExpiryElement=G,e.CardNumberElement=z,e.CheckoutProvider=D,e.CurrencySelectorElement=en,e.Elements=O,e.ElementsConsumer=F,e.EmbeddedCheckout=Y,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return S(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),l=t.useRef(null),a=t.useRef(null),s=u(t.useState({embeddedCheckout:null}),2),c=s[0],d=s[1];t.useEffect(function(){if(!a.current&&!l.current){var e=function(e){a.current||l.current||(a.current=e,l.current=a.current.initEmbeddedCheckout(r).then(function(e){d({embeddedCheckout:e})}))};"async"===i.tag&&!a.current&&(r.clientSecret||r.fetchClientSecret)?i.stripePromise.then(function(t){t&&e(t)}):"sync"===i.tag&&!a.current&&(r.clientSecret||r.fetchClientSecret)&&e(i.stripe)}},[i,r,c,a]),t.useEffect(function(){return function(){c.embeddedCheckout?(l.current=null,c.embeddedCheckout.destroy()):l.current&&l.current.then(function(){l.current=null,c.embeddedCheckout&&c.embeddedCheckout.destroy()})}},[c.embeddedCheckout]),t.useEffect(function(){A(a)},[a]);var f=g(n);t.useEffect(function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[f,n]);var p=g(r);return t.useEffect(function(){if(null!=p){if(null==r)return void console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=p.clientSecret&&r.clientSecret!==p.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=p.fetchClientSecret&&r.fetchClientSecret!==p.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=p.onComplete&&r.onComplete!==p.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=p.onShippingDetailsChange&&r.onShippingDetailsChange!==p.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=p.onLineItemsChange&&r.onLineItemsChange!==p.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[p,r]),t.createElement(B.Provider,{value:c},o)},e.EpsBankElement=Q,e.ExpressCheckoutElement=et,e.FpxBankElement=X,e.IbanElement=$,e.IdealBankElement=Z,e.LinkAuthenticationElement=eo,e.P24BankElement=J,e.PaymentElement=ee,e.PaymentMethodMessagingElement=eu,e.PaymentRequestButtonElement=er,e.ShippingAddressElement=el,e.useCheckout=function(){N(t.useContext(R),"calls useCheckout()");var e=t.useContext(L);if(!e)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return e},e.useElements=function(){return j("calls useElements()").elements},e.useStripe=function(){return I("calls useStripe()").stripe}})(t,n(2115))},7368:(e,t,n)=>{"use strict";n.d(t,{c:()=>y});var r,o="basil",i="https://js.stripe.com",l="".concat(i,"/").concat(o,"/stripe.js"),u=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,a=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,s=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var n,r=e[t];if(n=r.src,u.test(n)||a.test(n))return r}return null},c=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(l).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},d=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:t})},f=null,p=null,h=null,m=function(e,t,n){if(null===e)return null;var r,i=t[0].match(/^pk_test/),l=3===(r=e.version)?"v3":r;i&&l!==o&&console.warn("Stripe.js@".concat(l," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(o,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var u=e.apply(void 0,t);return d(u,n),u},v=!1,g=function(){return r?r:r=(null!==f?f:(f=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var n,r=s();r?r&&null!==h&&null!==p&&(r.removeEventListener("load",h),r.removeEventListener("error",p),null==(n=r.parentNode)||n.removeChild(r),r=c(null)):r=c(null),h=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},p=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},r.addEventListener("load",h),r.addEventListener("error",p)}catch(e){t(e);return}})).catch(function(e){return f=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)})};Promise.resolve().then(function(){return g()}).catch(function(e){v||console.warn(e)});var y=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];v=!0;var r=Date.now();return g().then(function(e){return m(e,t,r)})}},8883:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9676:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},9869:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:f,...p}=e;return(0,r.createElement)("svg",{ref:t,...s,width:o,height:o,stroke:n,strokeWidth:l?24*Number(i)/Number(o):i,className:u("lucide",c),...!d&&!a(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:a,...s}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:u("lucide-".concat(o(l(e))),"lucide-".concat(e),a),...s})});return n.displayName=l(e),n}}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1814:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>w,A:()=>v});var a=t(5155),n=t(2115),s=t(5647),i=t(9535),u=t(9509);let l=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",o=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",c=()=>u.env.SUPABASE_SERVICE_ROLE_KEY||"",d=()=>{let e=l(),r=o();if(!e||!r)throw Error("Supabase URL and Anon Key are required");return(0,i.createBrowserClient)(e,r)};(()=>{let e=l(),r=o();if(!e||!r)throw Error("Supabase URL and Anon Key are required");return(0,s.UU)(e,r)})(),(()=>{let e=l(),r=c();if(!e||!r)throw Error("Supabase URL and Service Role Key are required");return(0,s.UU)(e,r,{auth:{autoRefreshToken:!1,persistSession:!1}})})();let h=(0,n.createContext)(void 0);function w(e){let{children:r}=e,[t,s]=(0,n.useState)(null),[i,u]=(0,n.useState)(null),[l,o]=(0,n.useState)(!0),c=d(),w=(0,n.useCallback)(async e=>{try{let{data:r,error:t}=await c.from("user_profiles").select("*").eq("id",e).single();if(t)return void console.error("Error fetching profile:",t);u(r)}catch(e){console.error("Error fetching profile:",e)}},[c]),v=async()=>{t&&await w(t.id)};(0,n.useEffect)(()=>{(async()=>{var e;let{data:{session:r}}=await c.auth.getSession();s(null!=(e=null==r?void 0:r.user)?e:null),(null==r?void 0:r.user)&&await w(r.user.id),o(!1)})();let{data:{subscription:e}}=c.auth.onAuthStateChange(async(e,r)=>{var t;s(null!=(t=null==r?void 0:r.user)?t:null),(null==r?void 0:r.user)?await w(r.user.id):u(null),o(!1)});return()=>e.unsubscribe()},[w,c.auth]);let f=async(e,r)=>{let{error:t}=await c.auth.signInWithPassword({email:e,password:r});return{error:t}},p=async(e,r)=>{let{error:t}=await c.auth.signUp({email:e,password:r});return{error:t}},y=async()=>{await c.auth.signOut()};return(0,a.jsx)(h.Provider,{value:{user:t,profile:i,loading:l,signIn:f,signUp:p,signOut:y,refreshProfile:v},children:r})}function v(){let e=(0,n.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5166:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5356,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,1814))},5356:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},e=>{var r=r=>e(e.s=r);e.O(0,[444,535,441,684,358],()=>r(5166)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1814:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>u,A:()=>h});var a=t(5155),r=t(2115),l=t(5647),n=t(9535),i=t(9509);let c=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",d=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",o=()=>i.env.SUPABASE_SERVICE_ROLE_KEY||"",x=()=>{let e=c(),s=d();if(!e||!s)throw Error("Supabase URL and Anon Key are required");return(0,n.createBrowserClient)(e,s)};(()=>{let e=c(),s=d();if(!e||!s)throw Error("Supabase URL and Anon Key are required");return(0,l.UU)(e,s)})(),(()=>{let e=c(),s=o();if(!e||!s)throw Error("Supabase URL and Service Role Key are required");return(0,l.UU)(e,s,{auth:{autoRefreshToken:!1,persistSession:!1}})})();let m=(0,r.createContext)(void 0);function u(e){let{children:s}=e,[t,l]=(0,r.useState)(null),[n,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),o=x(),u=async e=>{try{let{data:s,error:t}=await o.from("user_profiles").select("*").eq("id",e).single();if(t)return void console.error("Error fetching profile:",t);i(s)}catch(e){console.error("Error fetching profile:",e)}},h=async()=>{t&&await u(t.id)};(0,r.useEffect)(()=>{(async()=>{var e;let{data:{session:s}}=await o.auth.getSession();l(null!=(e=null==s?void 0:s.user)?e:null),(null==s?void 0:s.user)&&await u(s.user.id),d(!1)})();let{data:{subscription:e}}=o.auth.onAuthStateChange(async(e,s)=>{var t;l(null!=(t=null==s?void 0:s.user)?t:null),(null==s?void 0:s.user)?await u(s.user.id):i(null),d(!1)});return()=>e.unsubscribe()},[]);let g=async(e,s)=>{let{error:t}=await o.auth.signInWithPassword({email:e,password:s});return{error:t}},b=async(e,s)=>{let{error:t}=await o.auth.signUp({email:e,password:s});return{error:t}},j=async()=>{await o.auth.signOut()};return(0,a.jsx)(m.Provider,{value:{user:t,profile:n,loading:c,signIn:g,signUp:b,signOut:j,refreshProfile:h},children:s})}function h(){let e=(0,r.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2188:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>U});var a=t(5155),r=t(2115),l=t(4355),n=t(306),i=t(9676),c=t(1814),d=t(9869),o=t(5339),x=t(646),m=t(1007),u=t(4186);function h(e){let{onNeedAuth:s,onNeedCredits:t}=e,[n,i]=(0,r.useState)(null),[h,g]=(0,r.useState)(null),[b,j]=(0,r.useState)(!1),[p,y]=(0,r.useState)(null),[f,N]=(0,r.useState)(null),[v,w]=(0,r.useState)(""),A=(0,r.useRef)(null),{user:S,profile:C}=(0,c.A)(),k=e=>{i(e),y(null),w("");let s=new FileReader;s.onload=e=>{var s;g(null==(s=e.target)?void 0:s.result)},s.readAsDataURL(e)},E=async()=>{if(n){j(!0),w("");try{let e=new FormData;e.append("image",n);let a=await fetch("/api/analyze",{method:"POST",body:e}),r=await a.json();if(!a.ok)throw 429===a.status&&(r.needsCredits?t():r.isAnonymous&&s()),Error(r.error||"Analysis failed");y(r.analysis),N(r.usage)}catch(e){w(e instanceof Error?e.message:"An error occurred")}finally{j(!1)}}};return(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)("div",{onDrop:e=>{e.preventDefault();let s=e.dataTransfer.files;s.length>0&&k(s[0])},onDragOver:e=>e.preventDefault(),className:"border-2 border-dashed rounded-2xl p-8 text-center transition-colors ".concat(h?"border-blue-300 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),children:h?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("img",{src:h,alt:"Preview",className:"max-w-full max-h-64 mx-auto rounded-lg shadow-md"}),(0,a.jsxs)("div",{className:"flex justify-center space-x-3",children:[(0,a.jsx)("button",{onClick:E,disabled:b,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),(0,a.jsx)("span",{children:"Analyzing..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Analyze Age"})]})}),(0,a.jsx)("button",{onClick:()=>{i(null),g(null),y(null),w(""),A.current&&(A.current.value="")},className:"bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Choose Different Photo"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload a Photo"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop your photo here, or click to select"}),(0,a.jsx)("button",{onClick:()=>{var e;return null==(e=A.current)?void 0:e.click()},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Select Photo"})]}),(0,a.jsx)("input",{ref:A,type:"file",accept:"image/*",onChange:e=>{let s=e.target.files;s&&s.length>0&&k(s[0])},className:"hidden"})]})}),v&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-red-900",children:"Error"}),(0,a.jsx)("p",{className:"text-red-700 text-sm",children:v})]})]}),p&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-2xl p-6 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-green-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Complete"})]}),p.hasPersonDetected?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Estimated Age"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[p.estimatedAge," years"]})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Confidence"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-900",children:[Math.round(100*p.confidence),"%"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Analysis Details"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:p.explanation})]})]}):(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"No Person Detected"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:p.explanation})]})]}),f&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900",children:f.isAnonymous?"Daily Limit":"Remaining Uses"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:f.isAnonymous?"".concat(f.remainingUses," free analysis remaining today"):"".concat(f.remainingUses," analyses remaining")})]}),f.needsCredits&&(0,a.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Buy Credits"})]})})]})}var g=t(2355),b=t(3052);function j(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[n,d]=(0,r.useState)(""),[x,h]=(0,r.useState)(1),[j,p]=(0,r.useState)(1),{user:y}=(0,c.A)(),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;if(y){l(!0),d("");try{let t=await fetch("/api/history?page=".concat(e,"&limit=10"));if(!t.ok)throw Error("Failed to fetch history");let a=await t.json();s(a.analyses),h(a.pagination.page),p(a.pagination.totalPages)}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{l(!1)}}};(0,r.useEffect)(()=>{f()},[y]);let N=e=>{f(e)};return y?t?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading your history..."})]}):n?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-red-500"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error Loading History"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:n}),(0,a.jsx)("button",{onClick:()=>f(x),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Try Again"})]}):0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Analysis History"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your age analysis history will appear here after you upload photos."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Analysis History"})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>{var s,t;return(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3 mb-2",children:e.estimated_age?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:[e.estimated_age," years old"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",Math.round(100*(e.confidence_score||0)),"% confidence)"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-4 h-4 text-yellow-600"})}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:"No person detected"})]})}),(null==(s=e.analysis_result)?void 0:s.explanation)&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.analysis_result.explanation}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,a.jsx)(u.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:(t=e.created_at,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(t)))})]})]})})},e.id)})}),j>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>N(x-1),disabled:x<=1,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",x," of ",j]}),(0,a.jsxs)("button",{onClick:()=>N(x+1),disabled:x>=j,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(b.A,{className:"w-4 h-4"})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sign In to View History"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Create an account to keep track of your age analyses."})]})}var p=t(9074),y=t(1586),f=t(4835);function N(e){var s,t;let{onBuyCredits:l}=e,{user:n,profile:i,signOut:d}=(0,c.A)(),[o,x]=(0,r.useState)(!1);if(!n||!i)return null;let u=Math.max(0,3-i.daily_uses),h=i.last_use_date!==new Date().toISOString().split("T")[0];return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>x(!o),className:"flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:null==(s=n.email)?void 0:s.split("@")[0]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:1===(t=i.credits)?"1 credit":"".concat(t," credits")})]})]}),o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>x(!1)}),(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:n.email}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Member since ",new Date(i.created_at).toLocaleDateString()]})]})]})}),(0,a.jsxs)("div",{className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Daily Uses"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-blue-900 mt-1",children:[h?3:u,"/3"]}),(0,a.jsx)("div",{className:"text-xs text-blue-700",children:h?"Refreshed today":"Remaining today"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Credits"})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-green-900 mt-1",children:i.credits}),(0,a.jsx)("div",{className:"text-xs text-green-700",children:"Extra analyses"})]})]}),(0,a.jsxs)("button",{onClick:()=>{l(),x(!1)},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2",children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Buy More Credits"})]}),(0,a.jsxs)("button",{onClick:()=>{d(),x(!1)},className:"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})}var v=t(313),w=t(4416),A=t(8883),S=t(2919);function C(e){let{isOpen:s,onClose:t,initialMode:l="signin"}=e,[n,i]=(0,r.useState)(l),[d,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,b]=(0,r.useState)(""),{signIn:j,signUp:p}=(0,c.A)(),y=async e=>{e.preventDefault(),h(!0),b("");try{let{error:e}="signin"===n?await j(d,x):await p(d,x);e?b(e.message):"signup"===n?b("Check your email for the confirmation link!"):t()}catch(e){b("An unexpected error occurred")}finally{h(!1)}},f=()=>{o(""),m(""),b("")};return(0,a.jsxs)(v.lG,{open:s,onClose:t,className:"relative z-50",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,a.jsxs)(v.lG.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)(v.lG.Title,{className:"text-xl font-semibold",children:"signin"===n?"Sign In":"Create Account"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("form",{onSubmit:y,className:"p-6 space-y-4",children:[g&&(0,a.jsx)("div",{className:"p-3 rounded-lg text-sm ".concat(g.includes("Check your email")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"),children:g}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{id:"email",type:"email",value:d,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(S.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{id:"password",type:"password",value:x,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0,minLength:6})]})]}),(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?"Loading...":"signin"===n?"Sign In":"Create Account"}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"button",onClick:()=>{i("signin"===n?"signup":"signin"),f()},className:"text-sm text-blue-600 hover:text-blue-700 transition-colors",children:"signin"===n?"Don't have an account? Sign up":"Already have an account? Sign in"})})]})]})})]})}var k=t(5196),E=t(7368),P=t(5855);let I=(0,E.c)("your_stripe_publishable_key_here");function _(e){let{onSuccess:s,onError:t}=e,l=(0,P.useStripe)(),n=(0,P.useElements)(),[i,d]=(0,r.useState)(!1),{refreshProfile:o}=(0,c.A)(),x=async e=>{if(e.preventDefault(),l&&n){d(!0);try{let e=await fetch("/api/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({packageType:"basic"})}),{clientSecret:a,error:r}=await e.json();if(r)return void t(r);let{error:i}=await l.confirmCardPayment(a,{payment_method:{card:n.getElement(P.CardElement)}});i?t(i.message||"Payment failed"):(await o(),s())}catch(e){t("Payment processing failed")}finally{d(!1)}}};return(0,a.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-900",children:"5 Credits Package"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Perfect for occasional use"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:"$5.00"}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"$1.00 per credit"})]})]})}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-3",children:(0,a.jsx)(P.CardElement,{options:{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}}}}})}),(0,a.jsxs)("button",{type:"submit",disabled:!l||i,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:i?"Processing...":"Buy 5 Credits for $5.00"})]})]})}function D(e){let{isOpen:s,onClose:t}=e,[l,n]=(0,r.useState)(!1),[i,c]=(0,r.useState)(""),d=()=>{n(!1),c(""),t()};return(0,a.jsxs)(v.lG,{open:s,onClose:d,className:"relative z-50",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,a.jsxs)(v.lG.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)(v.lG.Title,{className:"text-xl font-semibold",children:"Buy Credits"}),(0,a.jsx)("button",{onClick:d,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"p-6",children:l?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(k.A,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Payment Successful!"}),(0,a.jsx)("p",{className:"text-gray-600",children:"5 credits have been added to your account."})]}):(0,a.jsxs)(a.Fragment,{children:[i&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-lg text-sm",children:i}),(0,a.jsx)(P.Elements,{stripe:I,children:(0,a.jsx)(_,{onSuccess:()=>{n(!0),c(""),setTimeout(()=>{n(!1),t()},2e3)},onError:e=>{c(e),n(!1)}})}),(0,a.jsx)("div",{className:"mt-4 text-xs text-gray-500 text-center",children:"Your payment is secured by Stripe. We don't store your card details."})]})})]})})]})}function U(){let[e,s]=(0,r.useState)("upload"),[t,d]=(0,r.useState)(!1),[o,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)("signin"),{user:g,loading:b}=(0,c.A)(),p=()=>{u("signup"),d(!0)};return b?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Guess My Age"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"AI-powered age estimation"})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:g?(0,a.jsx)(N,{onBuyCredits:()=>x(!0)}):(0,a.jsxs)("button",{onClick:()=>{u("signin"),d(!0)},className:"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Sign In"})]})})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex space-x-1 bg-white rounded-lg p-1 mb-8 shadow-sm max-w-md mx-auto",children:[(0,a.jsxs)("button",{onClick:()=>s("upload"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("upload"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Upload Photo"})]}),(0,a.jsxs)("button",{onClick:()=>s("history"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("history"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"History"})]})]}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:"upload"===e?(0,a.jsx)(h,{onNeedAuth:p,onNeedCredits:()=>{g?x(!0):p()}}):(0,a.jsx)(j,{})})]}),(0,a.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Powered by OpenAI GPT-4o-mini • Built with Next.js and Supabase"}),(0,a.jsx)("p",{className:"text-gray-500 text-xs mt-2",children:"Your photos are analyzed securely and not stored on our servers"})]})})}),(0,a.jsx)(C,{isOpen:t,onClose:()=>d(!1),initialMode:m}),(0,a.jsx)(D,{isOpen:o,onClose:()=>x(!1)})]})}},7148:(e,s,t)=>{Promise.resolve().then(t.bind(t,2188))}},e=>{var s=s=>e(e.s=s);e.O(0,[535,493,441,684,358],()=>s(7148)),_N_E=e.O()}]);
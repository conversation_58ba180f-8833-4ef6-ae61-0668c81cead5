{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Browser client for client components\nexport const createSupabaseBrowserClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server client for server components and API routes\nexport const createSupabaseServerClient = () => {\n  const cookieStore = cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n      set(name: string, value: string, options: any) {\n        cookieStore.set({ name, value, ...options })\n      },\n      remove(name: string, options: any) {\n        cookieStore.set({ name, value: '', ...options })\n      },\n    },\n  })\n}\n\n// Service role client for admin operations\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\n// Database types\nexport interface UserProfile {\n  id: string\n  email: string | null\n  credits: number\n  daily_uses: number\n  last_use_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PhotoAnalysis {\n  id: string\n  user_id: string | null\n  image_url: string | null\n  estimated_age: number | null\n  confidence_score: number | null\n  analysis_result: any\n  created_at: string\n}\n\nexport interface CreditTransaction {\n  id: string\n  user_id: string\n  amount: number\n  credits_added: number\n  stripe_payment_intent_id: string | null\n  status: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;;;AAIoB;AAJpB;AACA;AAAA;AAAA;AACA;;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,6BAA6B;IACxC,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AACF;AAGO,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACtC,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createSupabaseBrowserClient } from '@/lib/supabase'\nimport type { UserProfile } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseBrowserClient()\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('user_profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error)\n        return\n      }\n\n      setProfile(data)\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n    }\n  }\n\n  const refreshProfile = async () => {\n    if (user) {\n      await fetchProfile(user.id)\n    }\n  }\n\n  useEffect(() => {\n    const getSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      }\n      \n      setLoading(false)\n    }\n\n    getSession()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          await fetchProfile(session.user.id)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    refreshProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3C,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,aAAa,KAAK,EAAE;QAC5B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC;oBAEA,WAAW;gBACb;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAjGgB;KAAA;AAmGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}
import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { analyzeImageAge, validateImageFile } from '@/lib/openai'
import { checkUsageLimit, consumeUse } from '@/lib/credits'
import { getClientIP } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    const ipAddress = getClientIP(request)

    // Check usage limits
    const usageCheck = await checkUsageLimit(userId, ipAddress)
    
    if (!usageCheck.canUse) {
      return NextResponse.json(
        { 
          error: usageCheck.needsCredits 
            ? 'You have reached your daily limit. Purchase credits to continue.' 
            : 'Daily limit reached. Please try again tomorrow or create an account for more uses.',
          needsCredits: usageCheck.needsCredits,
          isAnonymous: usageCheck.isAnonymous
        },
        { status: 429 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 })
    }

    // Validate image file
    const validation = validateImageFile(file)
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer()
    const base64 = Buffer.from(arrayBuffer).toString('base64')

    // Analyze image with OpenAI
    const analysis = await analyzeImageAge(base64)
    
    if (analysis.error) {
      return NextResponse.json({ error: analysis.error }, { status: 500 })
    }

    // Consume the use (daily limit or credit)
    const consumed = await consumeUse(userId, ipAddress)
    if (!consumed) {
      return NextResponse.json(
        { error: 'Failed to process request. Please try again.' },
        { status: 500 }
      )
    }

    // Store analysis result in database
    const analysisData = {
      user_id: userId,
      image_url: null, // We're not storing the actual image for privacy
      estimated_age: analysis.estimatedAge,
      confidence_score: analysis.confidence,
      analysis_result: {
        hasPersonDetected: analysis.hasPersonDetected,
        explanation: analysis.explanation,
        timestamp: new Date().toISOString()
      }
    }

    const { data: savedAnalysis, error: saveError } = await supabase
      .from('photo_analyses')
      .insert(analysisData)
      .select()
      .single()

    if (saveError) {
      console.error('Error saving analysis:', saveError)
      // Don't fail the request if we can't save to history
    }

    // Get updated usage info
    const updatedUsageCheck = await checkUsageLimit(userId, ipAddress)

    return NextResponse.json({
      success: true,
      analysis: {
        estimatedAge: analysis.estimatedAge,
        confidence: analysis.confidence,
        hasPersonDetected: analysis.hasPersonDetected,
        explanation: analysis.explanation
      },
      usage: {
        remainingUses: updatedUsageCheck.remainingUses,
        isAnonymous: updatedUsageCheck.isAnonymous,
        needsCredits: updatedUsageCheck.needsCredits
      },
      analysisId: savedAnalysis?.id
    })

  } catch (error) {
    console.error('Analysis API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

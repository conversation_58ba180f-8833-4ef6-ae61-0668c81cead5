import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase'
import { checkUsageLimit } from '@/lib/credits'
import { getClientIP } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient()
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    const ipAddress = getClientIP(request)

    // Check usage limits
    const usageCheck = await checkUsageLimit(userId, ipAddress)
    
    // Get user profile if authenticated
    let profile = null
    if (userId) {
      const { data } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      profile = data
    }

    return NextResponse.json({
      canUse: usageCheck.canUse,
      remainingUses: usageCheck.remainingUses,
      isAnonymous: usageCheck.isAnonymous,
      needsCredits: usageCheck.needsCredits,
      user: user ? {
        id: user.id,
        email: user.email,
        profile: profile
      } : null
    })

  } catch (error) {
    console.error('Usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

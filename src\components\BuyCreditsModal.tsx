'use client'

import { useState } from 'react'
import { Dialog } from '@headlessui/react'
import { X, CreditCard, Check } from 'lucide-react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { CREDIT_PACKAGES } from '@/lib/stripe'
import { formatPrice } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface BuyCreditsModalProps {
  isOpen: boolean
  onClose: () => void
}

function CheckoutForm({ onSuccess, onError }: { onSuccess: () => void; onError: (error: string) => void }) {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const { refreshProfile } = useAuth()

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!stripe || !elements) return
    
    setLoading(true)

    try {
      // Create payment intent
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ packageType: 'basic' })
      })

      const { clientSecret, error } = await response.json()
      
      if (error) {
        onError(error)
        return
      }

      // Confirm payment
      const { error: confirmError } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement)!,
        }
      })

      if (confirmError) {
        onError(confirmError.message || 'Payment failed')
      } else {
        // Refresh user profile to show new credits
        await refreshProfile()
        onSuccess()
      }
    } catch (err) {
      onError('Payment processing failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-blue-900">5 Credits Package</h3>
            <p className="text-sm text-blue-700">Perfect for occasional use</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-900">$5.00</div>
            <div className="text-sm text-blue-700">$1.00 per credit</div>
          </div>
        </div>
      </div>

      <div className="border border-gray-300 rounded-lg p-3">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
            },
          }}
        />
      </div>

      <button
        type="submit"
        disabled={!stripe || loading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
      >
        <CreditCard className="w-4 h-4" />
        <span>{loading ? 'Processing...' : 'Buy 5 Credits for $5.00'}</span>
      </button>
    </form>
  )
}

export default function BuyCreditsModal({ isOpen, onClose }: BuyCreditsModalProps) {
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState('')

  const handleSuccess = () => {
    setSuccess(true)
    setError('')
    setTimeout(() => {
      setSuccess(false)
      onClose()
    }, 2000)
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
    setSuccess(false)
  }

  const handleClose = () => {
    setSuccess(false)
    setError('')
    onClose()
  }

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl">
          <div className="flex items-center justify-between p-6 border-b">
            <Dialog.Title className="text-xl font-semibold">
              Buy Credits
            </Dialog.Title>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6">
            {success ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Payment Successful!
                </h3>
                <p className="text-gray-600">
                  5 credits have been added to your account.
                </p>
              </div>
            ) : (
              <>
                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-lg text-sm">
                    {error}
                  </div>
                )}

                <Elements stripe={stripePromise}>
                  <CheckoutForm onSuccess={handleSuccess} onError={handleError} />
                </Elements>

                <div className="mt-4 text-xs text-gray-500 text-center">
                  Your payment is secured by Stripe. We don't store your card details.
                </div>
              </>
            )}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}

import { createSupabaseServerClient } from './supabase-server'
import { createSupabaseAdminClient } from './supabase'

export interface UsageCheck {
  canUse: boolean
  remainingUses: number
  isAnonymous: boolean
  needsCredits: boolean
}

// Check if user can make an analysis
export async function checkUsageLimit(userId?: string, ipAddress?: string): Promise<UsageCheck> {
  const supabase = await createSupabaseServerClient()
  
  if (userId) {
    // Registered user - check daily limit and credits
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (!profile) {
      return { canUse: false, remainingUses: 0, isAnonymous: false, needsCredits: false }
    }
    
    // Reset daily uses if it's a new day
    const today = new Date().toISOString().split('T')[0]
    const lastUseDate = profile.last_use_date
    
    let dailyUses = profile.daily_uses
    if (lastUseDate !== today) {
      dailyUses = 0
      await supabase
        .from('user_profiles')
        .update({ daily_uses: 0, last_use_date: today })
        .eq('id', userId)
    }
    
    // Check if user has daily uses remaining (3 per day)
    if (dailyUses < 3) {
      return { 
        canUse: true, 
        remainingUses: 3 - dailyUses, 
        isAnonymous: false, 
        needsCredits: false 
      }
    }
    
    // Check if user has credits
    if (profile.credits > 0) {
      return { 
        canUse: true, 
        remainingUses: profile.credits, 
        isAnonymous: false, 
        needsCredits: false 
      }
    }
    
    return { 
      canUse: false, 
      remainingUses: 0, 
      isAnonymous: false, 
      needsCredits: true 
    }
  } else {
    // Anonymous user - check IP-based daily limit (1 per day)
    if (!ipAddress) {
      return { canUse: false, remainingUses: 0, isAnonymous: true, needsCredits: false }
    }
    
    const today = new Date().toISOString().split('T')[0]
    const supabaseAdmin = createSupabaseAdminClient()
    const { data: anonymousUses } = await supabaseAdmin
      .from('anonymous_uses')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('use_date', today)
    
    const usesToday = anonymousUses?.length || 0
    const canUse = usesToday < 1
    
    return { 
      canUse, 
      remainingUses: canUse ? 1 : 0, 
      isAnonymous: true, 
      needsCredits: false 
    }
  }
}

// Consume a use (either daily use or credit)
export async function consumeUse(userId?: string, ipAddress?: string): Promise<boolean> {
  const supabase = await createSupabaseServerClient()
  
  if (userId) {
    // Registered user
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (!profile) return false
    
    // Check if it's a new day and reset daily uses
    const today = new Date().toISOString().split('T')[0]
    let dailyUses = profile.daily_uses
    
    if (profile.last_use_date !== today) {
      dailyUses = 0
    }
    
    // Use daily limit first, then credits
    if (dailyUses < 3) {
      await supabase
        .from('user_profiles')
        .update({ 
          daily_uses: dailyUses + 1, 
          last_use_date: today,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
      return true
    } else if (profile.credits > 0) {
      await supabase
        .from('user_profiles')
        .update({ 
          credits: profile.credits - 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
      return true
    }
    
    return false
  } else {
    // Anonymous user
    if (!ipAddress) return false
    
    const today = new Date().toISOString().split('T')[0]
    const supabaseAdmin = createSupabaseAdminClient()

    // Check if already used today
    const { data: existingUse } = await supabaseAdmin
      .from('anonymous_uses')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('use_date', today)
      .single()
    
    if (existingUse) return false
    
    // Record the use
    await supabaseAdmin
      .from('anonymous_uses')
      .insert({ ip_address: ipAddress, use_date: today })
    
    return true
  }
}

// Add credits to user account
export async function addCredits(userId: string, credits: number): Promise<boolean> {
  const supabase = await createSupabaseServerClient()
  
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('credits')
    .eq('id', userId)
    .single()
  
  if (!profile) return false
  
  await supabase
    .from('user_profiles')
    .update({ 
      credits: profile.credits + credits,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
  
  return true
}

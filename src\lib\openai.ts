import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface AgeAnalysisResult {
  estimatedAge: number | null
  confidence: number
  hasPersonDetected: boolean
  explanation: string
  error?: string
}

export async function analyzeImageAge(imageBase64: string): Promise<AgeAnalysisResult> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Most cost-effective vision model
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this image and estimate the age of the person in it. 
              
              Please respond with a JSON object containing:
              - estimatedAge: number (the estimated age in years, or null if no person detected)
              - confidence: number (confidence level from 0 to 1)
              - hasPersonDetected: boolean (true if a person is clearly visible)
              - explanation: string (brief explanation of your analysis)
              
              If no person is detected or the image is unclear, set estimatedAge to null and hasPersonDetected to false.
              Be as accurate as possible with age estimation, considering facial features, skin texture, and other age indicators.
              
              Example response:
              {
                "estimatedAge": 25,
                "confidence": 0.8,
                "hasPersonDetected": true,
                "explanation": "Clear facial features visible, appears to be a young adult based on skin texture and facial structure"
              }`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "low" // Use low detail to reduce costs
              }
            }
          ]
        }
      ],
      max_tokens: 300,
      temperature: 0.1, // Low temperature for more consistent results
    })

    const content = response.choices[0]?.message?.content
    if (!content) {
      throw new Error('No response from OpenAI')
    }

    try {
      // Try to parse JSON response
      const result = JSON.parse(content)
      
      // Validate the response structure
      if (typeof result.hasPersonDetected !== 'boolean') {
        throw new Error('Invalid response format')
      }
      
      return {
        estimatedAge: result.estimatedAge,
        confidence: Math.max(0, Math.min(1, result.confidence || 0)),
        hasPersonDetected: result.hasPersonDetected,
        explanation: result.explanation || 'No explanation provided'
      }
    } catch (parseError) {
      // If JSON parsing fails, try to extract information from text
      const hasPersonMatch = content.toLowerCase().includes('person') || 
                           content.toLowerCase().includes('face') ||
                           content.toLowerCase().includes('human')
      
      const ageMatch = content.match(/(\d+)\s*years?\s*old/i) || 
                      content.match(/age.*?(\d+)/i) ||
                      content.match(/(\d+).*?age/i)
      
      const estimatedAge = ageMatch ? parseInt(ageMatch[1]) : null
      
      return {
        estimatedAge: hasPersonMatch ? estimatedAge : null,
        confidence: hasPersonMatch ? 0.5 : 0,
        hasPersonDetected: hasPersonMatch,
        explanation: content.substring(0, 200) + (content.length > 200 ? '...' : '')
      }
    }
  } catch (error) {
    console.error('OpenAI API error:', error)
    
    return {
      estimatedAge: null,
      confidence: 0,
      hasPersonDetected: false,
      explanation: 'Error analyzing image',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Helper function to convert File to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate image file
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024 // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  
  if (!allowedTypes.includes(file.type)) {
    return { 
      valid: false, 
      error: 'Please upload a valid image file (JPEG, PNG, or WebP)' 
    }
  }
  
  if (file.size > maxSize) {
    return { 
      valid: false, 
      error: 'Image file is too large. Please upload an image smaller than 10MB' 
    }
  }
  
  return { valid: true }
}

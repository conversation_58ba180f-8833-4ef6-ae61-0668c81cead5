import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
})

export const CREDIT_PACKAGES = {
  basic: {
    name: '5 Credits',
    credits: 5,
    price: 500, // $5.00 in cents
    description: 'Perfect for occasional use'
  }
} as const

export type CreditPackage = keyof typeof CREDIT_PACKAGES

export async function createPaymentIntent(
  userId: string,
  packageType: CreditPackage,
  userEmail?: string
): Promise<{ clientSecret: string; paymentIntentId: string }> {
  const package_ = CREDIT_PACKAGES[packageType]
  
  const paymentIntent = await stripe.paymentIntents.create({
    amount: package_.price,
    currency: 'usd',
    metadata: {
      userId,
      packageType,
      credits: package_.credits.toString(),
    },
    receipt_email: userEmail,
    description: `${package_.name} - Age Analysis Credits`,
  })

  return {
    clientSecret: paymentIntent.client_secret!,
    paymentIntentId: paymentIntent.id,
  }
}

export async function verifyPaymentIntent(paymentIntentId: string): Promise<{
  success: boolean
  userId?: string
  credits?: number
  error?: string
}> {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
    
    if (paymentIntent.status !== 'succeeded') {
      return { success: false, error: 'Payment not completed' }
    }
    
    const userId = paymentIntent.metadata.userId
    const credits = parseInt(paymentIntent.metadata.credits || '0')
    
    if (!userId || !credits) {
      return { success: false, error: 'Invalid payment metadata' }
    }
    
    return { success: true, userId, credits }
  } catch (error) {
    console.error('Stripe verification error:', error)
    return { success: false, error: 'Payment verification failed' }
  }
}

// Webhook signature verification
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string
): Stripe.Event | null {
  try {
    return stripe.webhooks.constructEvent(
      payload,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return null
  }
}

import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Browser client for client components
export const createSupabaseBrowserClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Service role client for admin operations (server-side only)
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Database types
export interface UserProfile {
  id: string
  email: string | null
  credits: number
  daily_uses: number
  last_use_date: string
  created_at: string
  updated_at: string
}

export interface PhotoAnalysis {
  id: string
  user_id: string | null
  image_url: string | null
  estimated_age: number | null
  confidence_score: number | null
  analysis_result: any
  created_at: string
}

export interface CreditTransaction {
  id: string
  user_id: string
  amount: number
  credits_added: number
  stripe_payment_intent_id: string | null
  status: string
  created_at: string
}

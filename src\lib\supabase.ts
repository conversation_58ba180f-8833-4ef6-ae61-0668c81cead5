import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

// Get environment variables with fallbacks
const getSupabaseUrl = () => process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const getSupabaseAnonKey = () => process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
const getSupabaseServiceKey = () => process.env.SUPABASE_SERVICE_ROLE_KEY || ''

// Client-side Supabase client (lazy initialization)
export const createSupabaseClient = () => {
  const url = getSupabaseUrl()
  const key = getSupabaseAnonKey()

  if (!url || !key) {
    throw new Error('Supabase URL and Anon Key are required')
  }

  return createClient(url, key)
}

// Browser client for client components
export const createSupabaseBrowserClient = () => {
  const url = getSupabaseUrl()
  const key = getSupabaseAnonKey()

  if (!url || !key) {
    throw new Error('Supabase URL and Anon Key are required')
  }

  return createBrowserClient(url, key)
}

// Service role client for admin operations (server-side only)
export const createSupabaseAdminClient = () => {
  const url = getSupabaseUrl()
  const serviceKey = getSupabaseServiceKey()

  if (!url || !serviceKey) {
    throw new Error('Supabase URL and Service Role Key are required')
  }

  return createClient(url, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Legacy exports for backward compatibility
export const supabase = createSupabaseClient()
export const supabaseAdmin = createSupabaseAdminClient()

// Database types
export interface UserProfile {
  id: string
  email: string | null
  credits: number
  daily_uses: number
  last_use_date: string
  created_at: string
  updated_at: string
}

export interface PhotoAnalysis {
  id: string
  user_id: string | null
  image_url: string | null
  estimated_age: number | null
  confidence_score: number | null
  analysis_result: any
  created_at: string
}

export interface CreditTransaction {
  id: string
  user_id: string
  amount: number
  credits_added: number
  stripe_payment_intent_id: string | null
  status: string
  created_at: string
}
